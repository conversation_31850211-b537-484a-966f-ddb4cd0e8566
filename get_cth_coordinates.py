
# import fitz  # PyMuPDF
# import pandas as pd
# from typing import List, Tuple, Dict
# import os

# def load_input_data(excel_path: str) -> pd.DataFrame:
#     df = pd.read_excel(excel_path)
#     df["CTH_list"] = df["CTH_list"].apply(lambda x: eval(x) if isinstance(x, str) else [])
#     return df

# def find_term_coordinates(doc: fitz.Document, term: str) -> List[Dict]:
#     matches = []
#     term_lower = term.lower()
#     for page_num, page in enumerate(doc, start=1):
#         text_instances = page.search_for(term, quads=True, flags=fitz.TEXT_PRESERVE_LIGATURES | fitz.TEXT_PRESERVE_WHITESPACE)
#         for inst in text_instances:
#             matches.append({
#                 "coordinates": str(inst.rect),
#                 "page_number": page_num,
#                 "quad_points": inst,
#                 "term": term
#             })
#     return matches

# def highlight_term_on_page(page: fitz.Page, quad_points: List[fitz.Quad]):
#     for quad in quad_points:
#         highlight = page.add_highlight_annot(quad)
#         highlight.update()

# def process_pdf(pdf_path: str, terms: List[str]) -> Tuple[List[Dict], str]:
#     results = []
#     highlighted_pdf_path = pdf_path.replace(".pdf", "_highlighted.pdf")
#     doc = fitz.open(pdf_path)
    
#     for term in terms:
#         matches = find_term_coordinates(doc, term)
#         for match in matches:
#             page = doc[match["page_number"] - 1]
#             highlight_term_on_page(page, [match["quad_points"]])
#             results.append({
#                 "pdf_path": pdf_path,
#                 "CTH": match["term"],
#                 "coordinates": match["coordinates"],
#                 "page_number": match["page_number"]
#             })

#     doc.save(highlighted_pdf_path)
#     doc.close()
#     return results, highlighted_pdf_path

# def process_all_pdfs(excel_path: str, output_excel_path: str) -> None:
#     df = load_input_data(excel_path)
#     all_results = []

#     for idx, row in df.iterrows():
#         pdf_path = row["pdf_path"]
#         cth_terms = row["CTH_list"]
#         if os.path.exists(pdf_path):
#             result, _ = process_pdf(pdf_path, cth_terms)
#             all_results.extend(result)
#         else:
#             print(f"PDF not found: {pdf_path}")

#     result_df = pd.DataFrame(all_results)
#     result_df.to_excel(output_excel_path, index=False)

# if __name__ == "__main__":
#     input_excel = "sample_curated_data.xlsx" 
#     output_excel = "cth_coordinates_output.xlsx"
#     process_all_pdfs(input_excel, output_excel)


###########################################################


import re
import fitz  # PyMuPDF
import pandas as pd
import os
import unicodedata
from typing import List, Tuple, Dict

# def normalize_text(text: str) -> str:
#     """Normalize ligatures and convert text to lower case."""
#     return unicodedata.normalize("NFKC", text).lower()
def normalize_text(text: str) -> str:
    """Lowercase and remove multiple spaces."""
    return re.sub(r"\s+", " ", text.strip().lower())

def extract_words_with_coords(page) -> List[Tuple[float, float, float, float, str]]:
    """Extract words with coordinates and dehyphenate."""
    return page.get_text("words", flags=fitz.TEXT_DEHYPHENATE)


def load_input_excel(excel_path: str) -> pd.DataFrame:
    df = pd.read_excel(excel_path)
    df["CTH_list"] = df["CTH_list"].apply(lambda x: eval(x) if isinstance(x, str) else [])
    return df

def search_term_in_page_0(term: str, page: fitz.Page) -> List[List[fitz.Rect]]:
    """Search for term in page, including multi-line handling and return list of coordinate sequences."""
    term = normalize_text(term)
    words = page.get_text("words")  # list of (x0, y0, x1, y1, word, block_no, line_no, word_no)
    words.sort(key=lambda w: (w[5], w[6], w[7]))  # sort by block, line, word
    word_texts = [normalize_text(w[4]) for w in words]

    term_words = term.split()
    results = []

    i = 0
    while i < len(word_texts):
        match_coords = []
        j = 0
        while j < len(term_words) and i + j < len(word_texts):
            if term_words[j] == word_texts[i + j]:
                match_coords.append(fitz.Rect(words[i + j][0:4]))
                j += 1
            else:
                break
        if j == len(term_words):
            results.append(match_coords)
            i += j
        else:
            i += 1
    return results

def search_term_in_page(term: str, page) -> List[List[fitz.Rect]]:
    """
    Find matches of the term in the PDF page.
    """
    normalized_term = normalize_text(term)
    term_words = normalized_term.split()
    words = extract_words_with_coords(page)
    word_texts = [normalize_text(w[4]) for w in words]
    all_matches = []
    i = 0
    while i <= len(word_texts) - len(term_words):
        if word_texts[i:i + len(term_words)] == term_words:
            rects = [fitz.Rect(words[i + j][0], words[i + j][1], words[i + j][2], words[i + j][3]) for j in range(len(term_words))]
            all_matches.append(rects)
            i += len(term_words)
        else:
            i += 1
    return all_matches



def group_rects_by_line_0(rects: List[fitz.Rect], y_tol: float = 1.0) -> List[fitz.Rect]:
    """Group nearby rects by y0 line and return merged bounding boxes."""
    if not rects:
        return []

    # Sort by y0
    rects = sorted(rects, key=lambda r: r.y0)
    grouped = []
    current_group = [rects[0]]

    for rect in rects[1:]:
        if abs(rect.y0 - current_group[-1].y0) <= y_tol:
            current_group.append(rect)
        else:
            grouped.append(current_group)
            current_group = [rect]
    grouped.append(current_group)

    # Merge bounding boxes in each group
    merged_rects = []
    for group in grouped:
        x0 = min(r.x0 for r in group)
        y0 = min(r.y0 for r in group)
        x1 = max(r.x1 for r in group)
        y1 = max(r.y1 for r in group)
        merged_rects.append(fitz.Rect(x0, y0, x1, y1))

    return merged_rects


def group_rects_by_line(rects: List[fitz.Rect], y_tol: float = 1.0) -> List[fitz.Rect]:
    """Group rects that belong to the same line based on y-coordinate proximity."""
    if not rects:
         return []
    rects = sorted(rects, key=lambda r: (r.y0, r.x0))
    groups = []
    current_group = [rects[0]]

    for r in rects[1:]:
        if abs(r.y0 - current_group[-1].y0) <= y_tol:
            current_group.append(r)
        else:
            groups.append(current_group)
            current_group = [r]
    groups.append(current_group)

    merged_rects = []
    for group in groups:
        x0 = min(r.x0 for r in group)
        y0 = min(r.y0 for r in group)
        x1 = max(r.x1 for r in group)
        y1 = max(r.y1 for r in group)
        merged_rects.append(fitz.Rect(x0, y0, x1, y1))

    return merged_rects





# def highlight_matches_on_page(page: fitz.Page, coords_list: List[List[fitz.Rect]]):
#     for rects in coords_list:
#         if rects:
#             highlight = page.add_highlight_annot(rects)
#             highlight.update()

def highlight_matches_on_page(page, rect_lists: List[List[fitz.Rect]]):
    """Highlight merged rectangles on the page."""
    for rects in rect_lists:
        merged = group_rects_by_line(rects)
        for rect in merged:
            page.add_highlight_annot(rect)

def process_pdf_0(pdf_path: str, terms: List[str]) -> Tuple[List[Dict], str]:
    doc = fitz.open(pdf_path)
    results = []
    output_pdf_path = pdf_path.replace(".pdf", "_highlighted.pdf")

    for term in terms:
        for page_num in range(len(doc)):
            page = doc[page_num]
            coord_lists = search_term_in_page(term, page)
            if coord_lists:
                highlight_matches_on_page(page, coord_lists)
                for coords in coord_lists:
                    # results.append({
                    #     "pdf_path": pdf_path,
                    #     "CTH": term,
                    #     "coordinates": [str(rect) for rect in coords],
                    #     "page_number": page_num + 1
                    # })
                    merged_coords = group_rects_by_line(coords)
                    results.append({
                        "pdf_path": pdf_path,
                        "CTH": term,
                        "coordinates": [str(r) for r in merged_coords],
                        "page_number": page_num + 1
                    })
    doc.save(output_pdf_path)
    doc.close()
    return results, output_pdf_path


def process_pdf(pdf_path: str, terms: List[str]) -> Tuple[List[Dict], str]:
    doc = fitz.open(pdf_path)
    results = []
    output_pdf_path = pdf_path.replace(".pdf", "_highlighted.pdf")
    for term in terms:
        for page_num in range(len(doc)):
            page = doc[page_num]
            coord_lists = search_term_in_page(term, page)

            if coord_lists:
                highlight_matches_on_page(page, coord_lists)
                for coords in coord_lists:
                    merged_coords = group_rects_by_line(coords)
                    results.append({
                        "pdf_path": pdf_path,
                        "CTH": term,
                        "coordinates": [str(r) for r in merged_coords],
                        "page_number": page_num + 1
                    })

    doc.save(output_pdf_path)
    doc.close()
    return results, output_pdf_path



def process_all_pdfs(input_excel_path: str, output_excel_path: str):
    df = load_input_excel(input_excel_path)
    all_results = []

    for _, row in df.iterrows():
        pdf_path = row["pdf_path"]
        term_list = row["CTH_list"]

        if os.path.exists(pdf_path):
            print(f"Processing: {pdf_path}")
            results, _ = process_pdf(pdf_path, term_list)
            all_results.extend(results)
        else:
            print(f"PDF not found: {pdf_path}")

    result_df = pd.DataFrame(all_results)
    result_df.to_excel(output_excel_path, index=False)
    print(f"\n✅ Output saved to: {output_excel_path}")

if __name__ == "__main__":
    input_excel = "sample_curated_data.xlsx" 
    output_excel = "cth_coordinates_output.xlsx"
    process_all_pdfs(input_excel, output_excel)
