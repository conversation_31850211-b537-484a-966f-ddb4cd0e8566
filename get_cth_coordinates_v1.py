

import re
import fitz  # PyMuPDF
import pandas as pd
import os
import unicodedata
from typing import List, Tuple, Dict


def normalize_text(text: str) -> str:
    """Lowercase and remove multiple spaces."""
    return re.sub(r"\s+", " ", text.strip().lower())

def extract_words_with_coords(page) -> List[Tuple[float, float, float, float, str]]:
    """Extract words with coordinates and dehyphenate."""
    return page.get_text("words", flags=fitz.TEXT_DEHYPHENATE)


def load_input_excel(excel_path: str) -> pd.DataFrame:
    df = pd.read_excel(excel_path)
    df["CTH_list"] = df["CTH_list"].apply(lambda x: eval(x) if isinstance(x, str) else [])
    return df

def search_term_in_page(term: str, page) -> List[List[fitz.Rect]]:
    """
    Find matches of the term in the PDF page.
    """
    normalized_term = normalize_text(term)
    term_words = normalized_term.split()
    words = extract_words_with_coords(page)
    word_texts = [normalize_text(w[4]) for w in words]
    all_matches = []
    i = 0
    while i <= len(word_texts) - len(term_words):
        if word_texts[i:i + len(term_words)] == term_words:
            rects = [fitz.Rect(words[i + j][0], words[i + j][1], words[i + j][2], words[i + j][3]) for j in range(len(term_words))]
            all_matches.append(rects)
            i += len(term_words)
        else:
            i += 1
    return all_matches



def group_rects_by_line(rects: List[fitz.Rect], y_tol: float = 1.0) -> List[fitz.Rect]:
    """Group rects that belong to the same line based on y-coordinate proximity."""
    if not rects:
         return []
    rects = sorted(rects, key=lambda r: (r.y0, r.x0))
    groups = []
    current_group = [rects[0]]

    for r in rects[1:]:
        if abs(r.y0 - current_group[-1].y0) <= y_tol:
            current_group.append(r)
        else:
            groups.append(current_group)
            current_group = [r]
    groups.append(current_group)

    merged_rects = []
    for group in groups:
        x0 = min(r.x0 for r in group)
        y0 = min(r.y0 for r in group)
        x1 = max(r.x1 for r in group)
        y1 = max(r.y1 for r in group)
        merged_rects.append(fitz.Rect(x0, y0, x1, y1))

    return merged_rects


def highlight_matches_on_page(page, rect_lists: List[List[fitz.Rect]]):
    """Highlight merged rectangles on the page."""
    for rects in rect_lists:
        merged = group_rects_by_line(rects)
        for rect in merged:
            page.add_highlight_annot(rect)


def process_pdf(pdf_path: str, terms: List[str]) -> Tuple[List[Dict], str]:
    doc = fitz.open(pdf_path)
    results = []
    output_pdf_path = pdf_path.replace(".pdf", "_highlighted.pdf")
    for term in terms:
        for page_num in range(len(doc)):
            page = doc[page_num]
            coord_lists = search_term_in_page(term, page)

            if coord_lists:
                highlight_matches_on_page(page, coord_lists)
                for coords in coord_lists:
                    merged_coords = group_rects_by_line(coords)
                    results.append({
                        "pdf_path": pdf_path,
                        "CTH": term,
                        "coordinates": [str(r) for r in merged_coords],
                        "page_number": page_num + 1
                    })

    doc.save(output_pdf_path)
    doc.close()
    return results, output_pdf_path


def process_all_pdfs(input_excel_path: str, output_excel_path: str):
    df = load_input_excel(input_excel_path)
    all_results = []

    for _, row in df.iterrows():
        pdf_path = row["pdf_path"]
        term_list = row["CTH_list"]

        if os.path.exists(pdf_path):
            print(f"Processing: {pdf_path}")
            results, _ = process_pdf(pdf_path, term_list)
            all_results.extend(results)
        else:
            print(f"PDF not found: {pdf_path}")

    result_df = pd.DataFrame(all_results)
    result_df.to_excel(output_excel_path, index=False)
    print(f"\n✅ Output saved to: {output_excel_path}")

if __name__ == "__main__":
    input_excel = "sample_curated_data.xlsx" 
    output_excel = "cth_coordinates_output.xlsx"
    process_all_pdfs(input_excel, output_excel)
