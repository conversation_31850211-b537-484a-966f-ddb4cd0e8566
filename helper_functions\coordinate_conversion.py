"""
Coordinate conversion utilities for PDF processing.

This module provides functions to convert between different coordinate systems:
- bbox coordinates (BOTTOMLEFT origin)
- fitz coordinates (TOPLEFT origin)

Author: <PERSON>
"""

def convert_bbox_to_fitz(bbox, page_height):
    """
    Convert bbox coordinates with BOTTOMLEFT origin to PyMuPDF (fitz) coordinates with TOPLEFT origin.
    
    Args:
        bbox (dict): Dictionary with 'l', 't', 'r', 'b' keys and BOTTOMLEFT origin
        page_height (float): Height of the page in points
        
    Returns:
        tuple: PyMuPDF coordinates (x0, y0, x1, y1) with TOPLEFT origin
    """
    # Extract values from bbox
    left = bbox["l"]
    top = bbox["t"]
    right = bbox["r"]
    bottom = bbox["b"]
    
    # Convert from BOTTOMLEFT to TOPLEFT origin
    # In BOTTOMLEFT, 'top' is distance from bottom
    # In TOPLEFT, y0 is distance from top
    fitz_y0 = page_height - top
    fitz_y1 = page_height - bottom
    
    # Return as fitz coordinates (x0, y0, x1, y1)
    return (left, fitz_y0, right, fitz_y1)

def convert_fitz_to_bbox(fitz_rect, page_height):
    """
    Convert PyMuPDF (fitz) coordinates with TOPLEFT origin to bbox coordinates with BOTTOMLEFT origin.
    
    Args:
        fitz_rect (tuple): PyMuPDF coordinates (x0, y0, x1, y1) with TOPLEFT origin
        page_height (float): Height of the page in points
        
    Returns:
        dict: Dictionary with 'l', 't', 'r', 'b' keys and BOTTOMLEFT origin
    """
    # Extract values from fitz_rect
    x0, y0, x1, y1 = fitz_rect
    
    # Convert from TOPLEFT to BOTTOMLEFT origin
    bbox_left = x0
    bbox_right = x1
    bbox_top = page_height - y0
    bbox_bottom = page_height - y1
    
    # Return as bbox dictionary
    return {
        "l": bbox_left,
        "t": bbox_top,
        "r": bbox_right,
        "b": bbox_bottom,
        "coord_origin": "BOTTOMLEFT"
    }


def example_conversion():
    # test page height 
    page_height = 808.0
    
    # test bbox
    bbox = {
        "l": 34.333333333333336,
        "t": 348.0,
        "r": 283.6666666666667,
        "b": 69.0,
        "coord_origin": "BOTTOMLEFT"
    }
    
    # Convert to fitz coordinates
    fitz_coords = convert_bbox_to_fitz(bbox, page_height)
    print(f"Fitz coordinates: {fitz_coords}")
    
    # Convert back to bbox
    converted_bbox = convert_fitz_to_bbox(fitz_coords, page_height)
    print(f"Converted back to bbox: {converted_bbox}")
