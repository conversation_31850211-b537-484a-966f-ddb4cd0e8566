import re

def filter_sentences_by_superscripts(full_text, superscript_list):
    """
    Filter sentences from the given text by removing those that contain superscript items.

    Args:
        full_text (str): The text to filter.
        superscript_list (list): List of superscript items.

    Returns:
        str: The filtered text with sentences containing superscripts removed.
    """
    # print(f"{full_text =}")
    # Split the content into sentences
    sentences = full_text.split(". ")
    
    # Filter out sentences containing any superscript item
    cleaned_sentences = [
        sentence for sentence in sentences
        if not any(superscript in sentence for superscript in superscript_list)
    ]
    
    # Join the cleaned sentences back together
    return ". ".join(cleaned_sentences)
