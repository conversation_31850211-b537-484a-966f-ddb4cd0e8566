"""
This module provides functionality to extract and process abstract content from academic papers.

The main function get_abstract() takes a list of content strings from the abstract section
and returns a dictionary containing the processed abstract text and its page location.

The module handles text formatting, cleanup of whitespace, and returns structured content
ready for further processing.
Author: <PERSON>adhav
Date: 06-12-24
"""

import re


def get_abstract(content_list, abstract_location):
    """
    Extract the abstract content from the abstract section of a paper.
    
    Args:
        content_list (list): List of dictionaries containing text and metadata from the abstract section.
        abstract_location (list): List of page numbers where the abstract is located.

    Returns:
        dict: A dictionary containing the abstract content with metadata preserved.
    """
    section_content = {}
    
    if content_list:
        # Create a list to store the abstract content with metadata
        abstract_content = []
        
        for item in content_list:
            if isinstance(item, dict):
                # Clean up text but preserve metadata
                cleaned_text = item.get("text", "").replace("-\n ", "-\n")
                
                abstract_content.append({
                    "text": cleaned_text,
                    "page_no": item.get("page_no", 0),
                    "fitz_coords": item.get("fitz_coords", {})
                })
        
        section_content["abstract"] = abstract_content
        section_content["page_number"] = abstract_location
        
        return section_content
    else:
        return {}
