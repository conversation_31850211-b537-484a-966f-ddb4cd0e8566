"""
This module provides functionality to extract and process conclusion sections from academic papers.

The main function get_conclusion() takes a list of text content from a conclusion section
and its location, processes the text by handling line breaks and formatting,
and returns a dictionary containing the processed conclusion text and page number.
Author: <PERSON>
Date: 06-12-24
"""


import re


def get_conclusion(content_list, conclusion_location):
    """
    Extract the conclusion content from the conclusion section of a paper.
    
    Args:
        content_list (list): List of dictionaries containing text and metadata from the conclusion section.
        conclusion_location (list): List of page numbers where the conclusion section is located.

    Returns:
        dict: A dictionary containing the conclusion content with metadata preserved.
    """
    section_content = {}
    
    if content_list:
        # Create a list to store the conclusion content
        full_text = []
        for item in content_list:
            item = item.replace("-\n ", "-\n")
            full_text.append(item)

        # Join the list into a single string
        section_content["conclusion"] = " ".join(full_text).strip()
        section_content["page_number"] = conclusion_location

        return section_content
    else:
        return {"conclusion": "", "page_number": []}
