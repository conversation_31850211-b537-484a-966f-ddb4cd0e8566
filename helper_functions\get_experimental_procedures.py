"""
This module contains functions for extracting and processing experimental procedures from scientific papers.
It provides functionality to clean and format experimental text content, handling various text formatting
issues like line breaks and spacing to produce standardized output.
Author: <PERSON>v
Date: 06-12-24
"""

import re


def get_experimental(content_list, experimental_location):
    """
    Extract the experimental content from the experimental section of a paper.
    
    Args:
        content_list (list): List of dictionaries containing text and metadata from the experimental section.
        experimental_location (list): List of page numbers where the experimental section is located.

    Returns:
        dict: A dictionary containing the experimental content with metadata preserved.
    """
    section_content = {}
    
    if content_list:
        # Create a list to store the experimental content with metadata
        experimental_content = []
        
        for item in content_list:
            if isinstance(item, dict):
                # Clean up text but preserve metadata
                cleaned_text = item.get("text", "").replace("-\n ", "-\n")
                
                experimental_content.append({
                    "text": cleaned_text,
                    "page_no": item.get("page_no", 0),
                    "fitz_coords": item.get("fitz_coords", {})
                })
        
        section_content["experimental_procedures"] = experimental_content
        section_content["page_number"] = experimental_location
        
        return section_content
    else:
        return {}
