"""
Extract figure titles and their locations from PDF documents.

This module provides functionality to identify and extract figure-related paragraphs
from PDF documents. It can detect various figure references including figures, schemes,
structures, diagrams, charts, photos and images, along with their associated page numbers
and section headers.

Dependencies:
    - get_headingwise_paragraphs: For extracting structured paragraphs from HTML

Author: Anand Jadhav
Date: 13-02-25
"""


## Imports required
import re



def extract_figure_paragraphs(header_paragraph, header_location):
    """
    Extract paragraphs starting with "Fig" patterns and include page numbers from header_location.

    Args:
        header_paragraph (dict): Dictionary with section headers as keys and lists of paragraphs as values.
        header_location (dict): Dictionary with section headers as keys and lists of pages as values.

    Returns:
        list: List of dictionaries with 'paragraph' and 'page_number' as keys.
    """
    # Define the regex pattern for matching "Fig" patterns
    fig_pattern = re.compile(
        r"^(figure|fig|scheme|structure|diagram|chart|photo|image)\.? ?(\d+[-\.\d]*[a-z]?|[IVXLCDM]*)\.?[:\)\]\-\,:;/]? ?(\d+[A-Z]?)?\s*[A-Za-z]?",
        re.IGNORECASE,
    )

    # Initialize the result list
    result_list = []
    # print(f"figures >>>{header_paragraph =}")
    # Iterate over headers and paragraphs
    for header, paragraphs in header_paragraph.items():

        # Find paragraphs starting with the "Fig" pattern
        fig_paragraphs = [
            para for para in paragraphs if fig_pattern.match(para.strip())
        ]

        # Retrieve page numbers for the current header
        # page_numbers = header_location.get(header, [])
        page_numbers= []
        # print(f"{page_numbers =}")

        # Pair each paragraph with its respective page number
        for paragraph in fig_paragraphs:
            result_list.append({
                "figure_title": paragraph.strip(),
                "page_number": [],
            })
    # print(f"{result_list =}")
    return result_list
