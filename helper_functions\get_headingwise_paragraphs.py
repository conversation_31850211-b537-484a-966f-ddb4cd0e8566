"""
This module provides functionality to extract and process paragraphs from PDF documents
with heading-wise organization. It handles PDF to HTML conversion while preserving
document structure, including paragraphs, superscripts, subscripts, and formatting.

The module uses PyMuPDF (fitz) for PDF processing and BeautifulSoup for HTML parsing,
enabling accurate extraction of text content while maintaining the hierarchical
organization of the document based on headings.

Author: Anand Jadhav
Date: 13-02-25
"""


import re
import fitz  # type: ignore
from bs4 import BeautifulSoup  # type: ignore
import time
import logging
from pprint import pprint




def pdf_to_html(pdf_path):
    """
    Convert a PDF file to HTML format with preserved paragraph structure,
    handling superscripts and subscripts.

    Args:
        pdf_path (str): Path to the input PDF file

    Returns:
        str: HTML string containing the formatted content of the PDF with paragraphs and line breaks
    """
    try:
        pdf_document = fitz.open(pdf_path)
        html_pages = []

        for page_number in range(len(pdf_document)):
            page = pdf_document[page_number]
            text_blocks = page.get_text("dict")["blocks"]

            page_html = "<div class='page'>"

            for block in text_blocks:
                # Type 0 indicates a text block
                if block["type"] == 0:
                    paragraph = []

                    for line in block["lines"]:
                        line_text_parts = []

                        for span in line["spans"]:
                            text = span["text"]
                            line_text_parts.append(text)

                        # Join the line text parts and add to paragraph if it’s not empty
                        line_text = " ".join(line_text_parts)  # .strip()
                        if line_text:
                            paragraph.append(line_text)

                    # Add completed paragraph to page HTML, handling the "■" case
                    if paragraph:
                        # full_paragraph = " ".join(paragraph)
                        full_paragraph = f"{'<br> '.join(paragraph)}"

                        # Check if "■" is within 30 characters from the end
                        if "■" in full_paragraph[-30:]:
                            # Split paragraph into two parts: before and after "■"
                            before_star = full_paragraph.rsplit("■", 1)[0].strip()
                            # Text after "■" starts with "■"
                            after_star = "■ " + full_paragraph.rsplit("■", 1)[1].strip()

                            # Add the first part as a separate paragraph
                            if before_star:
                                page_html += f"<p>{before_star}</p>"

                            # Add the second part (starting with "■") as a new paragraph
                            if after_star:
                                page_html += f"<p>{after_star}</p>"
                        else:
                            # Regular paragraph (no "■" within last 30 characters)
                            page_html += f"<p>{full_paragraph}</p>"

            page_html += "</div>"
            html_pages.append(page_html)

        # Combine all pages into a single HTML document
        full_html = "<html><body>" + " ".join(html_pages) + "</body></html>"

        # print(f"{full_html=}")

        full_html = (
            full_html.replace(" ′ -", "′-")
            .replace("  ", " ")
            .replace(" ′", "′")
            .replace(" - ", "-")
            # .replace(" -", "-")
            .replace("- ", "-")
            .replace("­", "")
        )

        # pdf_file_name = pdf_path.split("\\")[-1]

        # html_file = pdf_file_name.replace(".pdf", ".html")
        # with open(html_file, "w", encoding="utf-8") as f:

        #     f.write(full_html)
        #     print("PDF converted to HTML successfully.")

        return full_html
    except Exception as e:
        print(f"An error occurred: {e}")
        return None




def normalize_ligatures(text):
    """
    Removes extra spaces after ligatures in a text, joining the ligature correctly with the following characters.

    Args:
        text (str): The input text with potential ligature spacing issues.

    Returns:
        str: The normalized text with corrected ligatures.
    """
    # Define a regex pattern for common ligatures followed by unnecessary space and characters

    ligature_patterns = {
        r"ﬂ\s+": "fl",
        r"ﬁ\s+": "fi",
        r"ﬃ\s+": "ffi",
        r"ﬄ\s+": "ffl",
        r"ﬅ\s+": "ft",
        r"ﬆ\s+": "st",

        r"ﬂ": "fl",
        r"ﬁ": "fi",
        r"ﬃ": "ffi",
        r"ﬄ": "ffl",
        r"ﬅ": "ft",
        r"ﬆ": "st",
    }

    # Apply each replacement in the text
    for pattern, replacement in ligature_patterns.items():
        text = re.sub(pattern, replacement, text)

    # Use regex to remove hyphen followed by <br> or whitespace if it's between letters (not numbers)
    # text = re.sub(r"(?<=\w)-\s*<br>\s*(?=\w)", "", text)
    text = text.replace("</sup> <sup>", "</sup><sup>")
    return text




def write_paragraphs_to_file(header_paragraph_dict, output_file):
    """
    Writes the contents of header_paragraph_dict to a text file.

    Args:
        header_paragraph_dict (dict): Dictionary where keys are titles and values are lists of paragraphs
        output_file (str): Path to the text file to write the output
    """
    try:
        with open(output_file, "w", encoding="utf-8") as file:
            for title, paragraphs in header_paragraph_dict.items():
                # Write the title
                file.write(f"{title}\n")

                # Underline the title with '='
                file.write("=" * len(title) + "\n")

                # Write each paragraph under the title, separated by a newline
                for paragraph in paragraphs:
                    paragraph = paragraph.replace("  ", " ").replace(
                        "­", ""
                    )  # .strip()
                    # file.write(paragraph + " |END| \n\n")
                    file.write(paragraph + "\n\n")

                # Add a line separator between sections for clarity
                file.write("\n" + "-" * 50 + "\n\n")

        # print(f"Data successfully written to {output_file}")

    except Exception as error:

        logging.error("write_paragraphs_to_file Error: %s", str(error))




def create_title_regex(title):
    """
    Creates a regex pattern to match titles with various flexible formats.

    Args:
    - title (str): The title to create a regex for.

    Returns:
    - str: A regex pattern string.
    """
    # Convert title to regex that matches flexible formats
    title = re.escape(title)  # Escape special characters
    pattern = rf"""
        (?<!\w)                   # Ensure the match is not part of another word
        [A-Z0-9]*\s*[)|.]*\s*     # Allow prefixes like "1", "1A)", "IV)", "■", "2 |"
        {title}                   # Match the actual title text
        (\s|[A-Z])*               # Allow optional spacing or capitalization within the title
        [^\w\n]*                  # Allow any special characters after the title and before newline character
        (?:(?=\n)|(?=\s{2,}))     # Ensure the title is followed by a newline or multiple spaces
        (?!\w)                    # Ensure the match ends cleanly
    """
    return pattern

def extract_paragraphs_from_html(pdf_file, tan_name, title_list):
    article_size = "LARGE"
    try:
        # Convert PDF to HTML
        html = pdf_to_html(pdf_file)
        if html is None:
            print("Error converting PDF to HTML.")
            return None, None, 0, None

        # Parse the HTML content
        soup = BeautifulSoup(html, "html.parser")

        # Initialize dictionaries with the first title in each sublist as the key, and add 'PDF_INFO'
        header_location_dict = {title_group[0]: [] for title_group in title_list}
        header_paragraph_dict = {title_group[0]: [] for title_group in title_list}
        header_location_dict["PDF_INFO"] = []
        header_paragraph_dict["PDF_INFO"] = []

        # Find all pages in the HTML
        pages = soup.find_all("div", class_="page")
        total_pages = len(pages)  # Count the total number of pages

        current_title = None
        title_found_once = False  # Flag to indicate if a title was matched at least once

        # Iterate over each page
        for page_number, page in enumerate(pages):
            paragraphs = page.find_all("p")
            for paragraph in paragraphs:
                for br in paragraph.find_all("br"):
                    br.replace_with("\n")

                paragraph_text = paragraph.get_text().strip()
                if not paragraph_text:
                    continue  # Skip empty paragraphs

                # Normalize paragraph text
                paragraph_text = normalize_ligatures(paragraph_text)
                if not paragraph_text.endswith("\n"):
                    paragraph_text += "\n "
                paragraph_text = paragraph_text.replace(" ′ - ", "′-").replace(" ′", "′").replace(" - ", "-").replace(" -", "-").replace("- ", "-").replace("­", "")

                title_found = False
                for title_group in title_list:
                    for title in title_group:
                        title_regex = create_title_regex(title)
                        if re.search(title_regex, paragraph_text, re.VERBOSE):
                            current_title = title_group[0]
                            title_found = True
                            title_found_once = True
                            break
                        elif re.search(title_regex, paragraph_text[-(3 + len(title)):], re.IGNORECASE | re.VERBOSE):
                            current_title = title_group[0]
                            title_found = True
                            title_found_once = True
                            break
                    if title_found:
                        break

                # Assign paragraph to appropriate title
                if not title_found_once:
                    if page_number not in header_location_dict["PDF_INFO"]:
                        header_location_dict["PDF_INFO"].append(page_number)
                    header_paragraph_dict["PDF_INFO"].append(paragraph_text)
                elif current_title:
                    if page_number not in header_location_dict[current_title]:
                        header_location_dict[current_title].append(page_number)
                    header_paragraph_dict[current_title].append(paragraph_text)

        # Remove empty entries except for "PDF_INFO"
        header_location_dict = {k: v for k, v in header_location_dict.items() if v or k == "PDF_INFO"}
        header_paragraph_dict = {k: v for k, v in header_paragraph_dict.items() if v or k == "PDF_INFO"}

        # Generalize handling of unwanted page numbers
        for key in ["Conclusions", "Results and discussions"]:
            if key in header_location_dict and 0 in header_location_dict[key]:
                header_location_dict[key].remove(0)

        return header_location_dict, header_paragraph_dict, total_pages, article_size

    except Exception as error:
        logging.error("extract_paragraphs_from_html Error: %s", str(error))
        return None, None, 0, article_size




def extract_paragraphs_from_html_for_smaller_articles(pdf_file, tan_name, title_list):
    try:
        article_size = "SMALL"
        # print(f"SMALL FILE ***{pdf_file}***")
        # Convert PDF to HTML
        html = pdf_to_html(pdf_file)
        if html is None:
            # print("Error converting PDF to HTML.")
            return None, 0

        # Parse the HTML content
        soup = BeautifulSoup(html, "html.parser")

        # Initialize dictionary to store results
        all_text_dict = {}

        # Extract titles from the input title list
        acknowledgments_list = title_list[7]
        references_list = title_list[8]

        # Find all pages in the HTML
        pages = soup.find_all("div", class_="page")
        total_pages = len(pages)

        # Flag to indicate if a matching title is found
        matching_title_found = False

        # Iterate over each page
        for page_number, page in enumerate(pages):
            # print(f"{page_number = }")
            paragraphs = page.find_all("p")
            page_text = []  # Collect all text for the current page

            for paragraph in paragraphs:
                for br in paragraph.find_all("br"):
                    br.replace_with("\n")

                paragraph_text = paragraph.get_text().strip()
                paragraph_text = normalize_ligatures(paragraph_text)
                # print(f"2.{paragraph_text = }")

                if not paragraph_text.endswith("\n"):
                    paragraph_text = paragraph_text + "\n "

                paragraph_text = (paragraph_text
                                    .replace(" ′ - ", "′-")
                                    .replace("  ", " ")
                                    .replace(" ′", "′")
                                    .replace(" - ", "-")
                                    .replace(" -", "-")
                                    .replace("- ", "-")
                                    .replace("­", "")
                                )

                # Skip empty paragraphs
                if not paragraph_text:
                    continue

                # Check if the paragraph starts with any title
                for title_group in [acknowledgments_list, references_list]:
                    for title in title_group:
                        title_regex = create_title_regex(title)
                        # Check for title match using regex in the initial text
                        initial_text = paragraph_text[:5 + len(title)]
                        if re.match(title_regex, initial_text, re.IGNORECASE | re.VERBOSE):
                            matching_title_found = True
                            # print(f"Found matching title: {title}")
                            break
                    if matching_title_found:
                        break

                # Stop collecting paragraphs if a matching title is found
                if matching_title_found:
                    break

                # Add the paragraph to the current page's text
                page_text.append(paragraph_text)

            # Add the collected text and page number to the dictionary
            all_text_dict[f"page_{page_number}"] = {
                "text": " ".join(page_text),
                "page_number": [page_number],
            }

        # Drop entries with empty 'text' values
        all_text_dict = {key: value for key, value in all_text_dict.items() if value["text"].strip()}
        # print(f"{all_text_dict=}")

        return all_text_dict, total_pages, article_size

    except Exception as error:
        ### Log the error
        logging.error("extract_paragraphs_from_html_for_smaller_articles Error: %s", str(error))
        return None, 0, None


if  __name__ == "__main__":
    # Variations for different sections of a document
    intro_list = [
        "INTRODUCTION",
        "I N T R O D U C T I O N",
        "I n t r o d u c t i o n",
        "Introduction",
        "Background",
        "Introduction and Background",
        "B A C K G R O U N D",

    ]
    abstract_list = [
        "ABSTRACT",
        "Abstract",
        "A B S T R A C T",
        "a b s t r a c t",
        "Summary of Findings",
        "Abst.",
        "Summary",
        "S U M M A R Y",
    ]
    keywords_list = [
        "KEYWORDS",
        "Keywords",
        "K E Y W O R D S",
        "Key Terms",
        "Topics",
    ]
    experimental_list = [
        "EXPERIMENTAL PROCEDURES",
        "Experimental section",
        "Experimental Sections",
        "Experimental Section",
        "EXPERIMENTAL SECTION",
        "Experimental Section",
        "Experimental section",
        "■ EXPERIMENTAL SECTION",
        "EXPERIMENTAL PART",
        "Experimental Part",
        "Experimental part",
        "EXPERIMENTAL BIOLOGICAL PART",
        "EXPERIMENTAL CHEMICAL PART",
        "EXPERIMENTAL",
        "Experimental",
        "Experiment",
    ]
    results_list = [
        "Results and discussions",
        "R E S U L T S  A N D  D I S C U S S I O N",
        "CALCULATIONS, RESULTS, AND DISCUSSION",
        "EXPERIMENT RESULTS AND DISCUSSION",
        "Experiment results and discussion",
        "Experiment Results and Discussion",
        "Experiment Results And Discussion",
        "Experimental Results And Discussion",
        "Experimental results and discussion",
        "Experimental Results and Discussion",
        "Experimental Results and discussion",
        "RESULTS AND DISCUSSION",
        "RESULTS AND DISCUSSIONS",
        "RESULT AND DISCUSSION",
        "Results and Discussion",
        "Result and discussion",
        "Result and Discussion",
        "Results and Discussions",
        "Result and Discussions",
        "Experimental results",
        "Experimental Results",
        "EXPERIMENTAL RESULTS",
        "Results and discussion",
        "Findings",
        "Outcomes",
        "RESULTS",
        "R E S U L T S",
        "Results",
        "Result",
    ]
    materials_methods_list = [
        "Materials and methods",
        "Materials and Method",
        "Material and Methods",
        "Material and Method",
        "Methodology",
        "Methods",
        "2 | METHODS",
        "Materials",
    ]
    conclusion_list = [
        "Conclusions",
        "• CONCLUSIONS:",
        "CONCLUSION OR SUMMARY AND OUTLOOK",
        "Conclusion or summary and outlook",
        "Conclusion or Summary and Outlook",
        "Conclusions and future prospects",
        "Conclusions and Future Directions",
        "Conclusions and research needs",
        "Conclusion and future research",
        "Conclusions and perspectives",
        "Conclusions and Perspectives",
        "Conclusions and Discussions",
        "Conclusion and Discussion",
        "Conclusions and Discussion",
        "Conclusions and discussion",
        "C O N C L U S I O N S",
        "C O N C L U S I O N",
        "SUMMARY AND OUTLOOK",
        "Summary and Outlook",
        "Summary And Outlook",
        "Summary and outlook",
        "Conclusion",
        "CONCLUSIONS",
        "CONCLUSION",
    ]
    acknowledgments_list = [
        "ACKNOWLEDGMENTS",
        "ACKNOWLEDGEMENTS",
        "A C K N O W L E D G E M E N T S",
        "A C K N O W L E D G M E N T S",
        "Acknowledgements",
        "Acknowledgement",
        "Acknowledgments",
        "Acknowledgement",
        "Acknowlegments",
        "ACKNOWLEDGMENTS",
        "ACKNOWLEDGEMENTS",
        "ACKNOWLEGMENTS",
        "A C K N O W L E D G M E N T S",
        "A C K N O W L E D G E M E N T S",
        "A C K N O W L E G M E N T S",
    ]

    references_list = [
        "REFERENCES",
        "References and notes",
        "References and Notes",
        "Reference List",
        "R E F E R E N C E S",
        "R e f e r e n c e s",
        "References",
        "B I B L I O G R A P H Y",
        "B i b l i o g r a p h y",
        "Bibliography",
        "REFERENCES",
        "References",
        "BIBLIOGRAPHY",
        "B I B L I O G R A P H Y",
        "Bibliography",

    ]
    not_required_list = [ "Discussions", "DISCUSSIONS", "DISCUSSION","Discussion",]
    title_list = [
        intro_list,
        abstract_list,
        keywords_list,
        experimental_list,
        results_list,
        materials_methods_list,
        conclusion_list,
        acknowledgments_list,
        references_list,
        not_required_list
    ]



    pdf_file =  r"\\***********\bio-act-curation\MAC-Projects\Integrated-Indexing\shipments\982110\06000703B\06000703B.article.002.pdf"
    tan_name = "06000703B"
    extract_paragraphs_from_html(pdf_file, tan_name,  title_list)