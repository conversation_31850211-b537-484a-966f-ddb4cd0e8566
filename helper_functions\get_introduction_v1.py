
"""
This module provides functionality to extract and process the aim or purpose statements
from academic paper introductions. It includes methods to identify relevant sentences
containing aim-related keywords, clean and format the text, and return structured
aim statements while preserving the original context.

Author: <PERSON>v
Date: 29-05-25
"""

import re

import re

def get_aim_of_paper(intro_list):
    """
    Extract the aim of the paper from the introduction section by identifying keywords.
    Preserves original sentences with newline characters intact.
    
    Args:
        intro_list (list): List of dictionaries containing text and metadata from the introduction section.
    
    Returns:
        list: A list of dictionaries containing the aim of the paper with metadata preserved.
    """
    # Extract just the text for pattern matching
    text_only_list = []
    if isinstance(intro_list, list):
        for item in intro_list:
            if isinstance(item, dict) and "text" in item:
                text_only_list.append(item["text"])
    
    # Join all text items to get a complete string for pattern matching
    full_text = " ".join(text_only_list)

    # Define keywords and build a regex pattern to ignore line breaks and hyphenated line breaks
    keywords = [
        "investigates the potential of new approaches",
        "investigates cutting-edge technologies in",
        "focuses on enhancing the capabilities of",
        "focuses on addressing knowledge gaps in",
        "critical insights offered by the study",
        "examines the potential applications of",
        "examines the practical applications of",
        "main rationale for this investigation",
        "purposeful contributions of this work",
        "investigates the relationship between",
        "describes challenges and solutions in",
        "purpose of the experimental approach",
        "purpose behind the experimental work",
        "the analysis undertaken in this work",
        "scientific implications of the study",
        "purpose of the experimental research",
        "focuses on improving the accuracy of",
        "investigates the challenges faced in",
        "identifies potential improvements in",
        "motivation for conducting this work",
        "current work provides insights into",
        "results highlight the importance of",
        "rationale for the research approach",
        "purposeful exploration of the topic",
        "key hypothesis tested in this study",
        "methodological insights of the work",
        "findings from the experimental work",
        "this study elaborates on objectives",
        "investigates theoretical aspects of",
        "objective of this scientific study",
        "contributions of the present study",
        "key contributions of this research",
        "implications of the study findings",
        "hypothesis tested in this research",
        "outcomes derived from the analysis",
        "the investigation seeks to explore",
        "research focuses on the objectives",
        "rationale behind the investigation",
        "objectives of the current research",
        "offers a comprehensive analysis of",
        "clarifies the relationship between",
        "advances practical applications of",
        "proposes strategies for overcoming",
        "present study aims to investigate",
        "exploration of the research topic",
        "hypotheses evaluated in this work",
        "aims of the present investigation",
        "aims of the current investigation",
        "key aspects of this investigation",
        "methodology of this investigation",
        "current paper evaluates the goals",
        "research efforts focus on the aim",
        "demonstrates the effectiveness of",
        "offers an innovative solution for",
        "demonstrates the applicability of",
        "contributes to the development of",
        "explores new research avenues for",
        "primary aim of the investigation",
        "key findings of the research are",
        "importance of this investigation",
        "outcomes of the current research",
        "work contributes to the field of",
        "scientific contribution includes",
        "aim of the present investigation",
        "current investigation focuses on",
        "objectives outlined in the paper",
        "research underscores the purpose",
        "focuses on the implementation of",
        "advances the state of the art in",
        "develops and tests solutions for",
        "purpose of the present analysis",
        "research addresses the need for",
        "underlying purpose of the study",
        "methodology of the present work",
        "main focus of the current study",
        "main focus of the investigation",
        "focus of this experimental work",
        "investigates possible solutions",
        "provides empirical evidence for",
        "investigates the feasibility of",
        "provides a thorough analysis of",
        "provides critical insights into",
        "findings from the present work",
        "key points of the research are",
        "findings of this investigation",
        "contributions of this analysis",
        "hypotheses of the present work",
        "scope of this scientific study",
        "examines novel applications of",
        "proposes future directions for",
        "aims to bridge the gap between",
        "investigates the importance of",
        "objective of the present work",
        "focus of the current research",
        "hypothesis of the research is",
        "this investigation elucidates",
        "approach adopted in the study",
        "research underscores the need",
        "research framework focuses on",
        "objectives of this manuscript",
        "key contributions highlighted",
        "focus on the current findings",
        "purpose and aims of the study",
        "seeks to advance the field of",
        "focuses on the application of",
        "enhances the understanding of",
        "investigates new frontiers in",
        "explores new technologies for",
        "provides a new perspective on",
        "describes a novel approach to",
        "describes key developments in",
        "work is aimed at elucidating",
        "goals outlined in this paper",
        "investigation's primary goal",
        "the manuscript elaborates on",
        "aims of the present research",
        "aims of the current research",
        "purpose of the investigation",
        "main aim of the current work",
        "research objectives outlined",
        "goal of the current analysis",
        "work highlights key findings",
        "contributes to understanding",
        "highlights the importance of",
        "describes new techniques for",
        "introduces novel methods for",
        "evaluates the feasibility of",
        "investigates the dynamics of",
        "identifies gaps in knowledge",
        "assesses the implications of",
        "develops a new framework for",
        "assesses the contribution of",
        "investigates new methods for",
        "explores new perspectives on",
        "scope of this investigation",
        "aims and scope of the study",
        "study aims to shed light on",
        "focus area of this research",
        "primary questions addressed",
        "aim of the present research",
        "central goals of this study",
        "present analysis highlights",
        "current analysis elaborates",
        "explores new approaches for",
        "determines the viability of",
        "examines the limitations of",
        "proposes methods to address",
        "examines the feasibility of",
        "addresses key challenges in",
        "evaluates methodologies for",
        "main purpose of this study",
        "central theme of the paper",
        "analysis of the study aims",
        "importance of the findings",
        "significance of this study",
        "central findings discussed",
        "focus of the present study",
        "key insights of this study",
        "addresses the challenge of",
        "examines the effectiveness",
        "develops new insights into",
        "evaluates the potential of",
        "tests new hypotheses about",
        "the goal of this research",
        "study critically examines",
        "aims of the present study",
        "aims of the current study",
        "scope of the present work",
        "primary aim of this study",
        "study focuses on findings",
        "main aspects of this work",
        "goal of the present study",
        "central focus of the work",
        "tests the hypothesis that",
        "reveals new insights into",
        "presents new evidence for",
        "examines future trends in",
        "advances understanding of",
        "study seeks to determine",
        "investigation centers on",
        "current study emphasizes",
        "current paper focuses on",
        "the findings demonstrate",
        "work attempts to explain",
        "current work delves into",
        "aims of the present work",
        "aims of the current work",
        "aim of the present study",
        "addresses the problem of",
        "investigates the role of",
        "describes the effects of",
        "explores new avenues for",
        "proposes advancements in",
        "proposes improvements to",
        "assesses new findings in",
        "current study objectives",
        "key aspect of the study",
        "main research objective",
        "the intent of this work",
        "research theme involves",
        "current effort explores",
        "aim of the present work",
        "aim of the current work",
        "findings from the study",
        "purpose of the research",
        "objectives of this work",
        "provides a solution for",
        "proposes a new approach",
        "develops strategies for",
        "develops new models for",
        "explores the effects of",
        "describes the impact of",
        "explores innovations in",
        "methodological insights",
        "current work evaluates",
        "findings indicate that",
        "this work investigates",
        "objective of this work",
        "explores the potential",
        "analyzes the impact of",
        "assesses the impact of",
        "explores challenges in",
        "develops new tools for",
        "research contributions",
        "this study focuses on",
        "this research reveals",
        "emphasis of the study",
        "present investigation",
        "current investigation",
        "aim of the manuscript",
        "scope of the research",
        "scope of the analysis",
        "focuses on developing",
        "advances knowledge of",
        "proposes solutions to",
        "focuses on optimizing",
        "purpose investigation",
        "experimental research",
        "research delves into",
        "this paper presents",
        "analysis undertaken",
        "focus of this study",
        "aims to investigate",
        "research objectives",
        "research highlights",
        "this work outlines",
        "objective analyzed",
        "this study reveals",
        "this investigation",
        "present study aims",
        "aims of this paper",
        "analyzes trends in",
        "work contributions",
        "experimental study",
        "research rationale",
        "goal investigation",
        "research outlines",
        "research explores",
        "goal of this work",
        "experimental work",
        "research findings",
        "study methodology",
        "key contributions",
        "research targets",
        "findings suggest",
        "study elaborates",
        "paper highlights",
        "seeks to provide",
        "research purpose",
        "current findings",
        "current analysis",
        "study objectives",
        "current research",
        "paper objectives",
        "work highlights",
        "study evaluates",
        "aims to improve",
        "aims to resolve",
        "aims to enhance",
        "work evaluates",
        "study proposes",
        "scope involves",
        "study uncovers",
        "research theme",
        "objective work",
        "research goals",
        "work objective",
        "research focus",
        "study insights",
        "study research",
        "present study",
        "current study",
        "this research",
        "study purpose",
        "main findings",
        "research aims",
        "work analysis",
        "findings work",
        "work details",
        "current work",
        "present work",
        "key insights",
        "key findings",
        "study focus",
        "focus study",
        "study goals",
        "study scope",
        "this study",
        "study aims",
        "focus aims",
        "work focus",
        "work scope",
        "thework aims",
        "work aims",
        "this investigation",
    ]

    
    keywords_pattern = r"\b(" + "|".join(keywords) + r")\b"
    regex_pattern = re.compile(
        keywords_pattern.replace(" ", r"(?:[\s\-]*\n?)*"), re.IGNORECASE
    )

    # Search for the keyword in the full text
    match = regex_pattern.search(full_text)

    if match:
        print(f"{match.group(0) =}")
        
        # Find the starting position of the match
        start_pos = match.start()
        
        # Find which text item contains the match
        current_pos = 0
        match_item_index = 0
        
        for i, text in enumerate(text_only_list):
            if current_pos <= start_pos < current_pos + len(text):
                match_item_index = i
                break
            current_pos += len(text) + 1  # +1 for the space added during joining
        
        # Find the exact position within the matched text item
        relative_pos = start_pos - current_pos
        
        # Get the text item that contains the match
        match_item = intro_list[match_item_index]
        match_text = match_item["text"]
        
        # Create a new item with the text starting from the match position
        match_item_modified = {
            "text": match_text[relative_pos:],
            "page_no": match_item.get("page_no", 0),
            "fitz_coords": match_item.get("fitz_coords", {})
        }
        
        # Create a list to store the relevant text items with their metadata
        introduction_content = [match_item_modified]
        
        # Add all items after the match position
        for item in intro_list[match_item_index + 1:]:
            if isinstance(item, dict):
                introduction_content.append({
                    "text": item.get("text", ""),
                    "page_no": item.get("page_no", 0),
                    "fitz_coords": item.get("fitz_coords", {})
                })
        
        print(f"1..{introduction_content =}")
        return introduction_content

    else:
        # If no match found, get the last four items with their metadata
        if len(intro_list) > 4:
            last_four_items = intro_list[-4:]
        else:
            last_four_items = intro_list
        
        introduction_content = []
        for item in last_four_items:
            if isinstance(item, dict):
                introduction_content.append({
                    "text": item.get("text", ""),
                    "page_no": item.get("page_no", 0),
                    "fitz_coords": item.get("fitz_coords", {})
                })
        
        print(f"2..{introduction_content =}")
        return introduction_content


def get_aim_of_paper_0(intro_list):
    """
    Extract the aim of the paper from the introduction section by identifying keywords.
    Preserves original sentences with newline characters intact.
    Args:
        intro_list (list): List of dictionaries containing text and metadata from the introduction section.
    Returns:
        dict: A dictionary containing the aim of the paper with metadata preserved.
    """
    section_content = {}
    
    # Extract just the text for pattern matching
    text_only_list = []
    if isinstance(intro_list, list):
        for item in intro_list:
            if isinstance(item, dict) and "text" in item:
                text_only_list.append(item["text"])
    
    # Join all text items to get a complete string for pattern matching
    full_text = " ".join(text_only_list)

    # Define keywords and build a regex pattern to ignore line breaks and hyphenated line breaks
    keywords = [
        "investigates the potential of new approaches",
        "investigates cutting-edge technologies in",
        "focuses on enhancing the capabilities of",
        "focuses on addressing knowledge gaps in",
        "critical insights offered by the study",
        "examines the potential applications of",
        "examines the practical applications of",
        "main rationale for this investigation",
        "purposeful contributions of this work",
        "investigates the relationship between",
        "describes challenges and solutions in",
        "purpose of the experimental approach",
        "purpose behind the experimental work",
        "the analysis undertaken in this work",
        "scientific implications of the study",
        "purpose of the experimental research",
        "focuses on improving the accuracy of",
        "investigates the challenges faced in",
        "identifies potential improvements in",
        "motivation for conducting this work",
        "current work provides insights into",
        "results highlight the importance of",
        "rationale for the research approach",
        "purposeful exploration of the topic",
        "key hypothesis tested in this study",
        "methodological insights of the work",
        "findings from the experimental work",
        "this study elaborates on objectives",
        "investigates theoretical aspects of",
        "objective of this scientific study",
        "contributions of the present study",
        "key contributions of this research",
        "implications of the study findings",
        "hypothesis tested in this research",
        "outcomes derived from the analysis",
        "the investigation seeks to explore",
        "research focuses on the objectives",
        "rationale behind the investigation",
        "objectives of the current research",
        "offers a comprehensive analysis of",
        "clarifies the relationship between",
        "advances practical applications of",
        "proposes strategies for overcoming",
        "present study aims to investigate",
        "exploration of the research topic",
        "hypotheses evaluated in this work",
        "aims of the present investigation",
        "aims of the current investigation",
        "key aspects of this investigation",
        "methodology of this investigation",
        "current paper evaluates the goals",
        "research efforts focus on the aim",
        "demonstrates the effectiveness of",
        "offers an innovative solution for",
        "demonstrates the applicability of",
        "contributes to the development of",
        "explores new research avenues for",
        "primary aim of the investigation",
        "key findings of the research are",
        "importance of this investigation",
        "outcomes of the current research",
        "work contributes to the field of",
        "scientific contribution includes",
        "aim of the present investigation",
        "current investigation focuses on",
        "objectives outlined in the paper",
        "research underscores the purpose",
        "focuses on the implementation of",
        "advances the state of the art in",
        "develops and tests solutions for",
        "purpose of the present analysis",
        "research addresses the need for",
        "underlying purpose of the study",
        "methodology of the present work",
        "main focus of the current study",
        "main focus of the investigation",
        "focus of this experimental work",
        "investigates possible solutions",
        "provides empirical evidence for",
        "investigates the feasibility of",
        "provides a thorough analysis of",
        "provides critical insights into",
        "findings from the present work",
        "key points of the research are",
        "findings of this investigation",
        "contributions of this analysis",
        "hypotheses of the present work",
        "scope of this scientific study",
        "examines novel applications of",
        "proposes future directions for",
        "aims to bridge the gap between",
        "investigates the importance of",
        "objective of the present work",
        "focus of the current research",
        "hypothesis of the research is",
        "this investigation elucidates",
        "approach adopted in the study",
        "research underscores the need",
        "research framework focuses on",
        "objectives of this manuscript",
        "key contributions highlighted",
        "focus on the current findings",
        "purpose and aims of the study",
        "seeks to advance the field of",
        "focuses on the application of",
        "enhances the understanding of",
        "investigates new frontiers in",
        "explores new technologies for",
        "provides a new perspective on",
        "describes a novel approach to",
        "describes key developments in",
        "work is aimed at elucidating",
        "goals outlined in this paper",
        "investigation's primary goal",
        "the manuscript elaborates on",
        "aims of the present research",
        "aims of the current research",
        "purpose of the investigation",
        "main aim of the current work",
        "research objectives outlined",
        "goal of the current analysis",
        "work highlights key findings",
        "contributes to understanding",
        "highlights the importance of",
        "describes new techniques for",
        "introduces novel methods for",
        "evaluates the feasibility of",
        "investigates the dynamics of",
        "identifies gaps in knowledge",
        "assesses the implications of",
        "develops a new framework for",
        "assesses the contribution of",
        "investigates new methods for",
        "explores new perspectives on",
        "scope of this investigation",
        "aims and scope of the study",
        "study aims to shed light on",
        "focus area of this research",
        "primary questions addressed",
        "aim of the present research",
        "central goals of this study",
        "present analysis highlights",
        "current analysis elaborates",
        "explores new approaches for",
        "determines the viability of",
        "examines the limitations of",
        "proposes methods to address",
        "examines the feasibility of",
        "addresses key challenges in",
        "evaluates methodologies for",
        "main purpose of this study",
        "central theme of the paper",
        "analysis of the study aims",
        "importance of the findings",
        "significance of this study",
        "central findings discussed",
        "focus of the present study",
        "key insights of this study",
        "addresses the challenge of",
        "examines the effectiveness",
        "develops new insights into",
        "evaluates the potential of",
        "tests new hypotheses about",
        "the goal of this research",
        "study critically examines",
        "aims of the present study",
        "aims of the current study",
        "scope of the present work",
        "primary aim of this study",
        "study focuses on findings",
        "main aspects of this work",
        "goal of the present study",
        "central focus of the work",
        "tests the hypothesis that",
        "reveals new insights into",
        "presents new evidence for",
        "examines future trends in",
        "advances understanding of",
        "study seeks to determine",
        "investigation centers on",
        "current study emphasizes",
        "current paper focuses on",
        "the findings demonstrate",
        "work attempts to explain",
        "current work delves into",
        "aims of the present work",
        "aims of the current work",
        "aim of the present study",
        "addresses the problem of",
        "investigates the role of",
        "describes the effects of",
        "explores new avenues for",
        "proposes advancements in",
        "proposes improvements to",
        "assesses new findings in",
        "current study objectives",
        "key aspect of the study",
        "main research objective",
        "the intent of this work",
        "research theme involves",
        "current effort explores",
        "aim of the present work",
        "aim of the current work",
        "findings from the study",
        "purpose of the research",
        "objectives of this work",
        "provides a solution for",
        "proposes a new approach",
        "develops strategies for",
        "develops new models for",
        "explores the effects of",
        "describes the impact of",
        "explores innovations in",
        "methodological insights",
        "current work evaluates",
        "findings indicate that",
        "this work investigates",
        "objective of this work",
        "explores the potential",
        "analyzes the impact of",
        "assesses the impact of",
        "explores challenges in",
        "develops new tools for",
        "research contributions",
        "this study focuses on",
        "this research reveals",
        "emphasis of the study",
        "present investigation",
        "current investigation",
        "aim of the manuscript",
        "scope of the research",
        "scope of the analysis",
        "focuses on developing",
        "advances knowledge of",
        "proposes solutions to",
        "focuses on optimizing",
        "purpose investigation",
        "experimental research",
        "research delves into",
        "this paper presents",
        "analysis undertaken",
        "focus of this study",
        "aims to investigate",
        "research objectives",
        "research highlights",
        "this work outlines",
        "objective analyzed",
        "this study reveals",
        "this investigation",
        "present study aims",
        "aims of this paper",
        "analyzes trends in",
        "work contributions",
        "experimental study",
        "research rationale",
        "goal investigation",
        "research outlines",
        "research explores",
        "goal of this work",
        "experimental work",
        "research findings",
        "study methodology",
        "key contributions",
        "research targets",
        "findings suggest",
        "study elaborates",
        "paper highlights",
        "seeks to provide",
        "research purpose",
        "current findings",
        "current analysis",
        "study objectives",
        "current research",
        "paper objectives",
        "work highlights",
        "study evaluates",
        "aims to improve",
        "aims to resolve",
        "aims to enhance",
        "work evaluates",
        "study proposes",
        "scope involves",
        "study uncovers",
        "research theme",
        "objective work",
        "research goals",
        "work objective",
        "research focus",
        "study insights",
        "study research",
        "present study",
        "current study",
        "this research",
        "study purpose",
        "main findings",
        "research aims",
        "work analysis",
        "findings work",
        "work details",
        "current work",
        "present work",
        "key insights",
        "key findings",
        "study focus",
        "focus study",
        "study goals",
        "study scope",
        "this study",
        "study aims",
        "focus aims",
        "work focus",
        "work scope",
        "thework aims",
        "work aims",
        "this investigation",
    ]

    # Create a regex pattern to match keywords while ignoring interruptions
    keywords_pattern = r"\b(" + "|".join(keywords) + r")\b"
    regex_pattern = re.compile(
        keywords_pattern.replace(" ", r"(?:[\s\-]*\n?)*"), re.IGNORECASE
    )

    # Search for the keyword in the full text
    match = regex_pattern.search(full_text)

    if match:
        print(f"{match.group(0) =}")
        
        # Find the starting position of the match
        start_pos = match.start()
        
        # Find which text item contains the match
        current_pos = 0
        match_item_index = 0
        
        for i, text in enumerate(text_only_list):
            if current_pos <= start_pos < current_pos + len(text):
                match_item_index = i
                break
            current_pos += len(text) + 1  # +1 for the space we added when joining
        
        # Find the exact position within the matched text item
        relative_pos = start_pos - current_pos
        
        # Get the text item that contains the match
        match_item = intro_list[match_item_index]
        match_text = match_item["text"]
        
        # Create a new item with the text starting from the match position
        # and preserving all the metadata
        match_item_modified = {
            "text": match_text[relative_pos:],
            "page_no": match_item.get("page_no", 0),
            "fitz_coords": match_item.get("fitz_coords", {})
        }
        
        # Create a list to store the relevant text items with their metadata
        introduction_content = []
        
        # Add the modified match item
        introduction_content.append(match_item_modified)
        
        # Add all items after the match position
        for item in intro_list[match_item_index + 1:]:
            if isinstance(item, dict):
                introduction_content.append({
                    "text": item.get("text", ""),
                    "page_no": item.get("page_no", 0),
                    "fitz_coords": item.get("fitz_coords", {})
                })
        
        section_content["introduction"] = introduction_content
        print(f"1..{section_content =}")
        return section_content
    else:
        # If no match found, get the last four items with their metadata
        if len(intro_list) > 4:
            last_four_items = intro_list[-4:]
        else:
            last_four_items = intro_list
        
        introduction_content = []
        for item in last_four_items:
            if isinstance(item, dict):
                introduction_content.append({
                    "text": item.get("text", ""),
                    "page_no": item.get("page_no", 0),
                    "fitz_coords": item.get("fitz_coords", {})
                })
        
        section_content["introduction"] = introduction_content
        print(f"2..{section_content =}")
        return section_content





def get_last_four_items(input_string):
    """
    Splits the input string at '. ' and returns a string formed by the last 4 items, joined in the same order.

    Args:
        input_string (str): The string to process.

    Returns:
        str: A string consisting of the last 4 items from the input string, joined by '. '.
    """
    # Split the input string by '. '
    items = input_string.split(". ")

    # Get the last 4 items
    last_four = items[-4:]

    # Join the last 4 items with '. ' and return the result
    return ". ".join(last_four)




def filter_sentences_by_superscripts(full_text, superscript_list):
    """
    Filter sentences from the given text by removing those that contain superscript items.

    Args:
        full_text (str): The text to filter.
        superscript_list (list): List of superscript items.

    Returns:
        str: The filtered text with sentences containing superscripts removed.
    """
    # Split the content into sentences
    sentences = full_text.split(". ")

    # Filter out sentences containing any superscript item
    cleaned_sentences = [
        sentence for sentence in sentences
        if not any(superscript in sentence for superscript in superscript_list)
    ]

    # Join the cleaned sentences back together
    return ". ".join(cleaned_sentences)


# we need to update get_introduction.py module such that it will take "introduction" segment from input josn and then select the required text while preserving its metadata from json data.
