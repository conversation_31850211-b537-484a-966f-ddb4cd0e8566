"""
This module provides functionality to extract and process material_methods sections from academic papers.

The main function get_material_methods() takes a list of text content from a material_methods section
and its location, processes the text by handling line breaks and formatting,
and returns a dictionary containing the processed material_methods text and page number.

Author: <PERSON>hav
Date: 13-02-25
"""


import re


def get_materials(content_list, material_methods_location):
    """
    Extract the material_methods content from the material_methods section of a paper.
    
    Args:
        content_list (list): List of dictionaries containing text and metadata from the material_methods section.
        material_methods_location (list): List of page numbers where the material_methods section is located.

    Returns:
        dict: A dictionary containing the material_methods content with metadata preserved.
    """
    section_content = {}
    
    if content_list:
        # Create a list to store the material_methods content with metadata
        materials_content = []
        
        for item in content_list:
            if isinstance(item, dict):
                # Clean up text but preserve metadata
                cleaned_text = item.get("text", "").replace("-\n ", "-\n")
                
                materials_content.append({
                    "text": cleaned_text,
                    "page_no": item.get("page_no", 0),
                    "fitz_coords": item.get("fitz_coords", {})
                })
        
        section_content["material_methods"] = materials_content
        section_content["page_number"] = material_methods_location
        
        return section_content
    else:
        return {}
