"""
This module provides functionality to extract and process not_required_content sections from academic papers.

The main function not_required_content() takes a list of text content from a not_required_content section
and its location, processes the text by handling line breaks and formatting,
and returns a dictionary containing the processed not_required_content text and page number.

Author: <PERSON> Jadhav
Date: 13-02-25
"""


import re


def get_not_required_content(content_list, not_required_location):
    """
    Extract the not_required_content content from the material_methods section of a paper.
    
    Args:
        content_list (list): List of dictionaries containing text and metadata from the not_required section.
        not_required_location (list): List of page numbers where the not_required section is located.

    Returns:
        dict: A dictionary containing the not_required_content with metadata preserved.
    """
    section_content = {}
    
    if content_list:
        # Create a list to store the not_required content with metadata
        not_required_content = []
        
        for item in content_list:
            if isinstance(item, dict):
                # Clean up text but preserve metadata
                cleaned_text = item.get("text", "").replace("-\n ", "-\n")
                
                not_required_content.append({
                    "text": cleaned_text,
                    "page_no": item.get("page_no", 0),
                    "fitz_coords": item.get("fitz_coords", {})
                })
        
        section_content["not_required_content"] = not_required_content
        section_content["page_number"] = not_required_location
        
        return section_content
    else:
        return {}
