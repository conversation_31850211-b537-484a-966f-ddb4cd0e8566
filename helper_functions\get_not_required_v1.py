"""
This module provides functionality to extract and process not_required_content sections from academic papers.

The main function not_required_content() takes a list of text content from a not_required_content section
and its location, processes the text by handling line breaks and formatting,
and returns a dictionary containing the processed not_required_content text and page number.

Author: <PERSON> Jadhav
Date: 28-05-2025
"""


import re


def get_not_required_content(content_list, not_required_location):
    """
    Extract the not_required_content content from the not_required section of a paper.

    Args:
        content_list (list): List of dictionaries containing text and metadata from the not_required section.
        not_required_location (list): List of page numbers where the not_required section is located.

    Returns:
        dict: A dictionary containing 'section_text' and 'page_numbers' with preserved metadata.
    """
    section_text = []

    for item in content_list:
        if isinstance(item, dict):
            cleaned_text = item.get("text", "").replace("-\n ", "-\n")
            section_text.append({
                "text": cleaned_text,
                "page_no": item.get("page_no", 0),
                "fitz_coords": item.get("fitz_coords", {})
            })

    return {
        "section_text": section_text,
        "page_numbers": sorted(not_required_location)
    } if section_text else {}

