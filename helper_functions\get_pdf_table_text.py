"""
PDF Table Text Extraction Module

This module provides functionality to extract table data from PDF files along with their page numbers.
It includes capabilities to identify table headers, process table content, and handle duplicate tables
across multiple pages.

Key Features:
    - Extracts tables from PDF documents using PyMuPDF (fitz)
    - Identifies table headers using regex patterns
    - Removes duplicate tables that appear on multiple pages
    - Handles special formatting and cleaning of table text
    - Processes supporting information tables
    - Truncates tables based on consecutive alphabetical/alphanumeric patterns

Author: Anand Jadhav
Date: 06-12-24
"""


## Import libraries
import os
import re
# import pandas as pd  # type: ignore
import logging
import fitz  # type: ignore


def get_table_line(line):
    """Get table line from the line string.

    Args:
        line (_str_): line string from pdf file

    Returns:
        _bool_: True if table line else False
    """

    # Define the regular expression pattern for table lines
    table_pattern = re.compile(r'\b(Table|TABLE|T\s+A\s+B\s+L\s+E|T\s+a\s+b\s+l\s+e)\s+(\d+[A-Za-z]?|\w+|[IVXLCDM]+)(?::|\.|)\s*.*$', re.IGNORECASE)

    # Check if the line matches the table pattern
    if table_pattern.search(line):
        return line
    else:
        return None


def eliminate_duplicate_tables(result_list):
    """
    Eliminates duplicate table entries from `result_list` when the same table
    title appears on multiple pages.

    Args:
        result_list (list): List of dictionaries containing page numbers and table data.

    Returns:
        list: Filtered list of dictionaries with duplicates removed.
    """
    # To store unique table titles with the most valid data
    table_dict = {}

    # Iterate through each entry in the result_list
    for entry in result_list:
        table_data = entry["table_data"]
        page_num = entry["page_num"]

        # Extract table title (e.g., "Table 6") from the table data
        match = re.match(r"^\s*(Table\s+\d+)", table_data, re.IGNORECASE)

        ## Check if the table title is present in the table data
        if match:
            table_title = match.group(1).strip()

            if table_title in table_dict:
                # Compare the current table with the stored one to decide which to keep
                existing_entry = table_dict[table_title]

                if len(table_data) > len(existing_entry["table_data"]):

                    # Replace if the new table data is more detailed
                    table_dict[table_title] = entry
            else:
                # Add new table entry
                table_dict[table_title] = entry

    # Return the filtered list of table entries
    return list(table_dict.values())


def check_consecutive_alphabetical_list(
    table_data,
    start_line=20,
    consecutive_lines_threshold=15,
    min_consecutive_alphanumeric=10,
):
    """
    Check and truncate consecutive alphabetical and alphanumeric lines in a table string.

    Parameters:
    - table_data (str): Table data in string format.
    - start_line (int): The line number from which to start checking consecutive lines.
    - consecutive_lines_threshold (int): The threshold for the number of consecutive alphabetical lines to trigger truncation.
    - min_consecutive_alphanumeric (int): The minimum number of consecutive alphanumeric lines to trigger truncation.

    Returns:
    - str: Truncated table data based on the specified criteria.
    """
    try:
        # Initialize variables
        truncated_string = ""
        consecutive_count = 0
        consecutive_alpha_numeric_count = 0
        consecutive_threshold_reached = False

        # Clean and split the table data into lines
        table_data = table_data.replace("\n ", "\n").replace("  ", " ")
        lines = table_data.split("\n")

        # Iterate over each line in the table data
        for n, line in enumerate(lines):
            line = line.strip()

            # Include the lines before start_line in the truncated string
            if n < start_line:
                truncated_string += line + "\n"
                continue

            truncated_string += line + "\n"

            # Check if the line contains alphanumeric data
            if re.match(r"^[a-zA-Z0-9\s]+$", line) and len(line.split()) == 1:
                consecutive_alpha_numeric_count += 1

                # Check if the consecutive alphanumeric count meets the threshold
                if consecutive_alpha_numeric_count >= min_consecutive_alphanumeric:
                    consecutive_threshold_reached = True
                    break
            else:
                # Reset count if the line doesn't contain consecutive alphanumeric data
                consecutive_alpha_numeric_count = 0

            # Check if the line contains alphabetical data
            if re.match(r"^[a-zA-Z\s]+$", line):
                consecutive_count += 1

                if consecutive_count >= consecutive_lines_threshold:

                    # Exclude the last consecutive alphabetical lines from the truncated string
                    truncated_string = "\n".join(
                        lines[start_line : n - consecutive_lines_threshold]
                    )
                    ## Truncate the table data based on the threshold
                    consecutive_threshold_reached = True
                    break
            else:
                # Reset count if the line doesn't contain consecutive alphabetical data
                consecutive_count = 0

        # If the loop completes without reaching the threshold, return the entire string
        if not consecutive_threshold_reached:
            truncated_string = table_data

        return truncated_string.strip()

    except Exception as error:
        logging.error("check_consecutive_alphabetical_list Error: %s", str(error))
        return table_data



def extract_tables_with_page_numbers_correct(pdf_path):
    """
    Extract tables from a PDF file using PyMuPDF (fitz) and include page numbers.

    Args:
        pdf_path (str): Path to the PDF file.
        max_line_length (int): Maximum line length to consider for truncating table data.

    Returns:
        list: List of dictionaries, each containing the table content, page number.
    """

    ## Increase max_line_length if we are getting more noisy data and vice-a-versa.
    max_line_length = 70

    table_lines = []

    # Define the regular expression pattern to match table titles
    start_pattern = r"^\s*(Table|TABLE|T A B L E|T a b l e)\.?\s*\d+[:\.]?\s*.*$|^\s*(Table|TABLE|T A B L E|T a b l e)\s+(\.|\w)+\s*[:\.]?\s*.*$"

    # Open the PDF file using PyMuPDF
    with fitz.open(pdf_path)  as pdf_document:
        # List to store dictionaries for each table
        result_list = []

        # Iterate through each page in the PDF
        for page_num in range(pdf_document.page_count):

            # Extract text from the page
            page = pdf_document[page_num]

            # Extract the text from the page
            raw_text = page.get_text("text")

            # Remove different types of hyphens or dashes from the extracted text
            page_text = re.sub(r"[–—⁃−]", "-", raw_text)

            # Split the text into lines
            lines = page_text.split("\n")
            start_regex = re.compile(start_pattern)

            # Temporary storage for lines that might belong to a table
            current_table_lines = []

            # Flag to indicate whether we are inside a table
            inside_table = False

            # Counter to track the lines after the start pattern
            count_after_start_pattern = 0

            # Iterate through each line in the text
            for n, line in enumerate(lines):
                # Check if the line matches the start pattern
                table_line = get_table_line(line)

                ## check if the line is a table line
                if table_line:
                    new_line = lines[n].strip() + " " + lines[n + 1].strip()

                    if start_regex.match(new_line):
                        # If we were already inside a table, finalize the previous table
                        if inside_table:
                            table_data = "\n".join(current_table_lines)

                            # Check if the table data is not empty and add it to the result list
                            table_data = check_consecutive_alphabetical_list(table_data)
                            if table_data.strip():
                                table_data = table_data.replace("#", " ").replace("  ", " ")
                                result_list.append(
                                    {"page_num": page_num, "table_data": table_data}
                                )
                            # Reset the flag and clear the current table lines
                            current_table_lines = []

                        ## Check if the line matches the start pattern
                        inside_table = True
                        current_table_lines.append(line)
                        # Reset the counter
                        count_after_start_pattern = 0

                ## Check if the line matches the start pattern
                elif inside_table:
                    count_after_start_pattern += 1
                    current_table_lines.append(line)

                    # Check if we have passed the third line after the start pattern
                    if count_after_start_pattern > 4 or start_regex.match(line):
                        if len(line) > max_line_length:
                            table_data = "\n".join(current_table_lines[:-1])

                            ## Check if the table data is not empty and add it to the result list
                            table_data = check_consecutive_alphabetical_list(table_data)
                            if table_data.strip():
                                table_data = table_data.replace("#", " ").replace("  ", " ")
                                result_list.append(
                                    {"page_num": page_num, "table_data": table_data}
                                )
                            ## Reset the current table lines and set the flag to False
                            current_table_lines = []
                            inside_table = False
                            count_after_start_pattern = 0

            # Add any remaining lines after the last table
            if current_table_lines:
                table_data = "\n".join(current_table_lines)

                ## Check for consecutive alphabetical lists
                if table_data.strip():
                    table_data = table_data.replace("#", " ").replace("  ", " ")

                    ## Check if the table data is not empty and add it to the result list
                    result_list.append({"page_num": page_num, "table_data": table_data})

    ## Eliimate duplicate tables dictionaries
    result_list = eliminate_duplicate_tables(result_list)

    ## Return the list of dictionaries containing table data and page numbers
    return result_list



def extract_tables_with_page_numbers_1(pdf_path):
    """
    Extract tables from a PDF file using PyMuPDF (fitz) and include page numbers.

    Args:
        pdf_path (str): Path to the PDF file.

    Returns:
        list: List of dictionaries, each containing the table content, page number.
    """

    max_word_count = 14
    table_lines = []

    # Define the regular expression pattern to match table titles
    start_pattern = r"^\s*(Table|TABLE|T A B L E|T a b l e)\.?\s*\d+[:\.]?\s*.*$|^\s*(Table|TABLE|T A B L E|T a b l e)\s+(\.|\w)+\s*[:\.]?\s*.*$"

    # Open the PDF file using PyMuPDF
    with fitz.open(pdf_path) as pdf_document:
        # List to store dictionaries for each table
        result_list = []

        # Iterate through each page in the PDF
        for page_num in range(pdf_document.page_count):

            # Extract text from the page
            page = pdf_document[page_num]

            # Extract the text from the page
            raw_text = page.get_text("text")

            # Remove different types of hyphens or dashes from the extracted text
            page_text = re.sub(r"[–—⁃−]", "-", raw_text)

            # Split the text into lines
            lines = page_text.split("\n")
            start_regex = re.compile(start_pattern)

            # Temporary storage for lines that might belong to a table
            current_table_lines = []

            # Flag to indicate whether we are inside a table
            inside_table = False

            # Counter to track the lines after the start pattern
            count_after_start_pattern = 0

            # Iterate through each line in the text
            for n, line in enumerate(lines):
                # Check if the line matches the start pattern
                table_line = get_table_line(line)

                # Check if the line is a table line
                if table_line:
                    new_line = lines[n].strip() + " " + lines[n + 1].strip()

                    if start_regex.match(new_line):
                        # If we were already inside a table, finalize the previous table
                        if inside_table:
                            table_data = "\n".join(current_table_lines)

                            # Check if the table data is not empty and add it to the result list
                            table_data = check_consecutive_alphabetical_list(table_data)
                            if table_data.strip():
                                table_data = table_data.replace("#", " ").replace("  ", " ")
                                result_list.append(
                                    {"page_num": page_num, "table_data": table_data}
                                )
                            # Reset the flag and clear the current table lines
                            current_table_lines = []

                        # Check if the line matches the start pattern
                        inside_table = True
                        current_table_lines.append(line)
                        # Reset the counter
                        count_after_start_pattern = 0

                # Check if the line matches the start pattern
                elif inside_table:
                    count_after_start_pattern += 1
                    current_table_lines.append(line)

                    # Check if the line exceeds the word limit
                    if len(line.split()) > max_word_count:
                        table_data = "\n".join(current_table_lines[:-1])
                        print(f"{line =}")
                        # Check if the table data is not empty and add it to the result list
                        table_data = check_consecutive_alphabetical_list(table_data)
                        if table_data.strip():
                            table_data = table_data.replace("#", " ").replace("  ", " ")
                            result_list.append(
                                {"page_num": page_num, "table_data": table_data}
                            )
                        # Reset the current table lines and set the flag to False
                        current_table_lines = []
                        inside_table = False
                        count_after_start_pattern = 0

            # Add any remaining lines after the last table
            if current_table_lines:
                table_data = "\n".join(current_table_lines)

                # Check for consecutive alphabetical lists
                if table_data.strip():
                    table_data = table_data.replace("#", " ").replace("  ", " ")

                    # Check if the table data is not empty and add it to the result list
                    result_list.append({"page_num": page_num, "table_data": table_data})

    # Eliminate duplicate tables dictionaries
    result_list = eliminate_duplicate_tables(result_list)

    # Return the list of dictionaries containing table data and page numbers
    return result_list


def extract_tables_with_page_numbers_2(pdf_path):
    """
    Extract tables from a PDF file using PyMuPDF (fitz) and include page numbers.

    Args:
        pdf_path (str): Path to the PDF file.

    Returns:
        list: List of dictionaries, each containing the table content and page number.
    """

    # Parameters for conditions
    max_line_length = 80
    max_word_count = 13

    # List to store dictionaries for each table
    result_list = []

    # Define the regular expression pattern to match table titles
    start_pattern = r"^\s*(Table|TABLE|T A B L E|T a b l e)\.?\s*\d+[:\.]?\s*.*$|^\s*(Table|TABLE|T A B L E|T a b l e)\s+(\.|\w)+\s*[:\.]?\s*.*$"

    # Open the PDF file using PyMuPDF
    with fitz.open(pdf_path) as pdf_document:

        # Iterate through each page in the PDF
        for page_num in range(pdf_document.page_count):

            # Extract text from the page
            page = pdf_document[page_num]
            raw_text = page.get_text("text")

            # Remove different types of hyphens or dashes from the extracted text
            page_text = re.sub(r"[–—⁃−]", "-", raw_text)

            # Split the text into lines
            lines = page_text.split("\n")
            start_regex = re.compile(start_pattern)

            # Temporary storage for lines that might belong to a table
            current_table_lines = []

            # Flag to indicate whether we are inside a table
            inside_table = False

            # Counter to track the lines after the start pattern
            count_after_start_pattern = 0

            # Iterate through each line in the text
            for n, line in enumerate(lines):
                # Check if the line matches the start pattern
                table_line = get_table_line(line)

                if table_line:
                    new_line = lines[n].strip() + " " + lines[n + 1].strip()

                    if start_regex.match(new_line):
                        # If we were already inside a table, finalize the previous table
                        if inside_table:
                            table_data = "\n".join(current_table_lines)

                            # Check if the table data is not empty and add it to the result list
                            table_data = check_consecutive_alphabetical_list(table_data)
                            if table_data.strip():
                                table_data = table_data.replace("#", " ").replace("  ", " ")
                                result_list.append(
                                    {"page_num": page_num, "table_data": table_data}
                                )
                            # Reset the flag and clear the current table lines
                            current_table_lines = []

                        inside_table = True
                        current_table_lines.append(line)
                        # Reset the counter
                        count_after_start_pattern = 0

                elif inside_table:
                    count_after_start_pattern += 1

                    # Stop adding lines if either condition is met
                    if len(line) > max_line_length and len(line.split()) > max_word_count:
                        table_data = "\n".join(current_table_lines[:-1])
                        print(f"{len(line) =}, >> {len(line.split())=}")
                        # Check if the table data is not empty and add it to the result list
                        table_data = check_consecutive_alphabetical_list(table_data)
                        if table_data.strip():
                            table_data = table_data.replace("#", " ").replace("  ", " ")
                            result_list.append(
                                {"page_num": page_num, "table_data": table_data}
                            )

                        # Reset the current table lines and set the flag to False
                        current_table_lines = []
                        inside_table = False
                        count_after_start_pattern = 0

                    else:
                        current_table_lines.append(line)

            # Add any remaining lines after the last table
            if current_table_lines:
                table_data = "\n".join(current_table_lines)

                # Check for consecutive alphabetical lists
                if table_data.strip():
                    table_data = table_data.replace("#", " ").replace("  ", " ")

                    # Check if the table data is not empty and add it to the result list
                    result_list.append({"page_num": page_num, "table_data": table_data})

    # Eliminate duplicate table dictionaries
    result_list = eliminate_duplicate_tables(result_list)

    # Return the list of dictionaries containing table data and page numbers
    return result_list



def extract_tables_with_page_numbers(pdf_path, max_line_length=70, max_word_count=10):
    """
    Extract table data from a PDF file.

    Args:
        pdf_path (str): Path to the PDF file.
        max_line_length (int): Maximum line length to consider when collecting table data.
        max_word_count (int): Maximum number of words per line to consider when collecting table data.

    Returns:
        list: List of dictionaries with table data and page numbers.
    """
    table_data_list = []
    table_start_pattern = re.compile(
        r"^\s*(Table|TABLE|T A B L E|T a b l e)\.?\s*\d*[:\.]?\s*.*$"
    )  # Pattern to detect table headers

    with fitz.open(pdf_path) as pdf_document:
        # Iterate through pages
        for page_num in range(pdf_document.page_count):
            page = pdf_document[page_num]
            lines = page.get_text("text").split("\n")

            inside_table = False
            current_table_lines = []

            for line in lines:
                # Check for the table start pattern
                if not inside_table and table_start_pattern.match(line):
                    inside_table = True  # Start collecting table lines
                    current_table_lines.append(line)
                    continue

                # If inside a table, collect lines until a stopping condition is met
                if inside_table:
                    # Strip and analyze the line
                    stripped_line = line.strip()
                    word_count = len(stripped_line.split())

                    # Check stopping conditions
                    if (
                        len(stripped_line) > max_line_length  # Exceeds max line length
                        or word_count > max_word_count  # Exceeds max word count
                        or stripped_line == ""  # Blank line
                    ):
                        inside_table = False  # Stop collecting
                        if current_table_lines:
                            table_data = "\n".join(current_table_lines).strip()
                            table_data_list.append(
                                {"page_num": page_num + 1, "table_data": table_data}
                            )
                        current_table_lines = []  # Reset for next table
                    else:
                        current_table_lines.append(stripped_line)

            # Add remaining lines if any table data is left unprocessed
            if current_table_lines:
                table_data = "\n".join(current_table_lines).strip()
                table_data_list.append(
                    {"page_num": page_num + 1, "table_data": table_data}
                )

    return table_data_list


