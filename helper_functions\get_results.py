"""
This module contains functions for extracting and processing results content from research papers.
It provides utilities to clean and format text from results sections, handling various text
formatting issues like line breaks and spacing.
Author: <PERSON> Jadhav
Date: 06-12-24
"""

import re


def get_results(content_list, results_location):
    """
    Extract the results content from the results section of a paper.
    
    Args:
        content_list (list): List of dictionaries containing text and metadata from the results section.
        results_location (list): List of page numbers where the results section is located.

    Returns:
        dict: A dictionary containing the results content with metadata preserved.
    """
    section_content = {}
    
    if content_list:
        # Create a list to store the results content with metadata
        results_content = []
        
        for item in content_list:
            if isinstance(item, dict):
                # Clean up text but preserve metadata
                cleaned_text = item.get("text", "").replace("-\n ", "-\n")
                
                results_content.append({
                    "text": cleaned_text,
                    "page_no": item.get("page_no", 0),
                    "fitz_coords": item.get("fitz_coords", {})
                })
        
        section_content["results"] = results_content
        section_content["page_number"] = results_location
        
        return section_content
    else:
        return {}
