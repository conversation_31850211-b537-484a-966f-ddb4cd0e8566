"""
This module contains functions for extracting and processing results content from research papers.
It provides utilities to clean and format text from results sections, handling various text
formatting issues like line breaks and spacing.
Author: <PERSON> Jadhav
Date: 06-12-24
"""
from helper_functions.get_pdf_superscripts import get_all_superscripts

import re

def get_results_without_references(content_list, results_location, json_data):
    """
    Extract the results content from the results section of a paper and remove references.
    
    Args:
        content_list (dict): Dictionary containing results content
        results_location (list): List of page numbers where the results section is located
        json_data (dict): Complete JSON data containing all sections with metadata

    Returns:
        dict: A dictionary containing the filtered results content with metadata preserved
    """
    print(f"{content_list =}")
    section_content = {} 
    full_text = " "
    
    # Extract superscripts from the complete JSON data
    superscript_list = get_all_superscripts(json_data)
    print(f">>{superscript_list =}")
    
    content = content_list['results']
    full_text = content.replace("-\n ", "-\n")
    
    section_content["results"] = content.strip()
    section_content["page_number"] = results_location
    
    # Filter out sentences containing superscripts
    filtered_results = filter_sentences_by_superscripts(section_content["results"], superscript_list)
    section_content["results"] = filtered_results
    
    if len(filtered_results) > 10:
        return section_content
    else:
        return full_text



def filter_sentences_by_superscripts(full_text, superscript_list):
    """
    Filter sentences from the given text by removing those that contain superscript items.

    Args:
        full_text (str): The text to filter.
        superscript_list (list): List of superscript items.

    Returns:
        str: The filtered text with sentences containing superscripts removed.
    """
    # Split the content into sentences
    sentences = full_text.split(". ")

    # Filter out sentences containing any superscript item
    cleaned_sentences = [
        sentence for sentence in sentences
        if not any(superscript in sentence for superscript in superscript_list)
    ]

    # Join the cleaned sentences back together
    return ". ".join(cleaned_sentences)
