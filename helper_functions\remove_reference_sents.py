import re
import json
import fitz
# 


def extract_citations_from_json(json_file_path):
    """
    Extract all citation patterns from the JSON file to automatically detect superscripts.
    
    Args:
        json_file_path (str): Path to the JSON file
    
    Returns:
        list: List of unique citation patterns found in the text
    """
    try:
        with open(json_file_path, 'r', encoding='utf-8') as file:
            data = json.load(file)
    except:
        return []
    
    # Common citation patterns
    citation_patterns = []
    citation_regex = re.compile(r'\[[\d\-,\s]+\]')
    
    def find_citations_in_section(section_data):
        if isinstance(section_data, dict):
            for key, value in section_data.items():
                if key == "text" and isinstance(value, str):
                    citations = citation_regex.findall(value)
                    citation_patterns.extend(citations)
                elif isinstance(value, (dict, list)):
                    find_citations_in_section(value)
        elif isinstance(section_data, list):
            for item in section_data:
                find_citations_in_section(item)
    
    find_citations_in_section(data)
    return list(set(citation_patterns))  # Return unique citations

def normalize_superscripts_hyphens(superscripts_list):
    """
    Normalize all different types of hyphens in superscripts list to single hyphen.
    Handles various Unicode hyphens and encoded hyphen representations.
    
    Args:
        superscripts_list (list): List of citation superscripts that may contain various hyphen types
        
    Returns:
        list: Normalized list with all hyphen variants converted to single hyphen
    """
    normalized_superscripts = set()
    
    for s in superscripts_list:
        # Replace all different types of hyphens with single hyphen
        normalized_s = s
        
        # Replace various hyphen types: en dash, em dash, minus sign, figure dash, etc.
        hyphen_variants = ['–', '—', '−', '‒', '‑', '﹣', '－']
        for hyphen in hyphen_variants:
            normalized_s = normalized_s.replace(hyphen, '-')
        
        # Replace encoded hyphens (HTML entities and Unicode escapes)
        encoded_hyphens = ['&ndash;', '&mdash;', '&minus;', '\\u2013', '\\u2014', '\\u2212', '\\u2010', '\\u2011']
        for encoded in encoded_hyphens:
            normalized_s = normalized_s.replace(encoded, '-')
        
        # Add both original and normalized versions
        normalized_superscripts.add(s)
        normalized_superscripts.add(normalized_s)
    
    return list(normalized_superscripts)



def remove_citation_sentences_from_json(json_file_path, superscripts_list_raw):
    """
    Remove citation sentences from JSON file and clean citation markers from all text fields.
    
    Args:
        json_file_path (str): Path to the JSON file
        superscripts_list (list): List of citation patterns to remove
    
    Returns:
        dict: Updated JSON structure with cleaned text
    """
    # normalized_superscripts = set()
    # for s in superscripts_list:
    #     normalized_superscripts.add(s)
    #     normalized_superscripts.add(s.replace('–', '-'))
    #     # normalized_superscripts.add(s.replace('-', '–'))
    # print(f"{normalized_superscripts =}")
    # superscripts_list = list(normalized_superscripts)
    # print(f"{len(superscripts_list) =}")

    superscripts_list=normalize_superscripts_hyphens(superscripts_list_raw)
    print(f">>> { superscripts_list =}")
    # Load the JSON file
    try:
        with open(json_file_path, 'r', encoding='utf-8') as file:
            data = json.load(file)
    except FileNotFoundError:
        print(f"Error: File not found at {json_file_path}")
        return None
    except json.JSONDecodeError:
        print(f"Error: Invalid JSON format in {json_file_path}")
        return None
    
    # Join citation pattern
    citation_pattern = '|'.join(re.escape(c) for c in superscripts_list)
    citation_re = re.compile(citation_pattern)
    
    def clean_text(text):
        """Clean individual text content"""
        if not text or not isinstance(text, str):
            return text
            
        # Sentence split
        sentences = re.split(r'(?<=[.!?])\s+', text)
        drop_indices = set()

        for i, sent in enumerate(sentences):
            # Trim leading space for citation check
            trimmed = sent.strip()

            # Rule: outside-citation → sentence starts with citation (sentence.[1])
            if citation_re.match(trimmed) and i > 0:
                drop_indices.add(i - 1)
                continue

            # Rule: within-citation → citation followed by period in same sentence OR at end of sentence
            citations_in_sent = citation_re.findall(sent)
            for cit in citations_in_sent:
                for m in re.finditer(re.escape(cit), sent):
                    end = m.end()
                    after = sent[end:end+3]
                    # Check if citation is followed by period OR at the end of sentence
                    if re.match(r'^\s{0,2}\.', after) or end >= len(sent.strip()):
                        drop_indices.add(i)
                        break

            # Additional rule: Check if sentence ends with citation (even without period)
            if citations_in_sent:
                for cit in citations_in_sent:
                    # Create pattern to match citation at end of sentence (with optional whitespace)
                    end_pattern = re.escape(cit) + r'\s*$'
                    if re.search(end_pattern, sent):
                        drop_indices.add(i)
                        break

        # Rebuild filtered text and remove citation markers
        filtered_sentences = []
        for i in range(len(sentences)):
            if i not in drop_indices:
                # Remove citation markers from the sentence
                clean_sentence = citation_re.sub('', sentences[i])
                # Clean up extra spaces that might result from citation removal
                clean_sentence = re.sub(r'\s+', ' ', clean_sentence).strip()
                if clean_sentence:  # Only add non-empty sentences
                    filtered_sentences.append(clean_sentence)

        cleaned_data =  ' '.join(filtered_sentences).strip()    
        # print(f"{cleaned_data =}")     
        cleaned_data1 =remove_sentences_with_inbetween_citations(cleaned_data, superscripts_list)
        # print(f"{cleaned_data1 =}")
        return cleaned_data1
    
    def process_section(section_data):
        """Recursively process section data to find and clean text fields"""
        if isinstance(section_data, dict):
            processed_data = {}
            for key, value in section_data.items():
                if key == "text" and isinstance(value, str):
                    # Clean the text content
                    processed_data[key] = clean_text(value)
                elif isinstance(value, (dict, list)):
                    # Recursively process nested structures
                    processed_data[key] = process_section(value)
                else:
                    # Keep other fields as is
                    processed_data[key] = value
            return processed_data
        elif isinstance(section_data, list):
            return [process_section(item) for item in section_data]
        else:
            return section_data
    
    # Process the entire JSON structure
    cleaned_data = process_section(data)
    
    return cleaned_data

# def remove_inbetween_citation_sentences(json_data, superscripts_list):
#     """
#     Processes each item in json_data, extracts the "text" field, and removes
#     sentences that contain any citation from the superscripts_list.

#     Args:
#         json_data (list): A list of dictionaries with text data under the "text" field.
#         superscripts_list (list): A list of citation strings to check for (e.g., '[1]', '[13–15]').

#     Returns:
#         list: The same structure as json_data but with updated "text" fields where cited sentences are removed.
#     """

#     # Compile citation regex pattern from the list
#     citation_regex = re.compile(r'(' + '|'.join(map(re.escape, superscripts_list)) + r')')

#     # Sentence splitting pattern
#     sentence_splitter = re.compile(r'(?<=[.!?])\s+')

#     cleaned_json = []

#     for item in json_data:
#         updated_item = {}

#         for key, value in item.items():
#             if isinstance(value, dict) and "text" in value:
#                 original_text = value.get("text", "")
#                 sentences = sentence_splitter.split(original_text)
#                 filtered_sentences = [s for s in sentences if not citation_regex.search(s)]
#                 value["text"] = ' '.join(filtered_sentences)
#                 updated_item[key] = value
#             else:
#                 updated_item[key] = value  # Preserve non-dict fields or non-"text" fields

#         cleaned_json.append(updated_item)
#     if cleaned_json:
#         return cleaned_json
#     else: json_data

def remove_sentences_with_inbetween_citations(text, superscripts_list_raw):
    """
    Removes any sentence that contains a citation from the normalized citation list.
    """
    normalized_citations = normalize_superscripts_hyphens(superscripts_list_raw)
    normalized_citations.append('[14-15]')
    # Normalize hyphens in the text
    hyphen_variants = ['–', '—', '−', '‒', '‑', '﹣', '－']
    encoded_hyphens = ['&ndash;', '&mdash;', '&minus;', '\\u2013', '\\u2014', '\\u2212', '\\u2010', '\\u2011']

    normalized_text = text
    for hyphen in hyphen_variants + encoded_hyphens:
        normalized_text = normalized_text.replace(hyphen, '-')

    # Split text into sentences using punctuation followed by whitespace
    sentence_pattern = r'(?<=[.!?])\s+'
    sentences = re.split(sentence_pattern, normalized_text)

    # Compile regex pattern to match any citation
    escaped_citations = [re.escape(cite) for cite in normalized_citations]
    citation_pattern = re.compile('|'.join(escaped_citations))
    # print(f"{citation_pattern =}")
    # Remove sentences containing citations
    cleaned_sentences = [s for s in sentences if not citation_pattern.search(s)]

    return ' '.join(cleaned_sentences)


def save_cleaned_json(cleaned_data, output_file):
    """Save cleaned JSON data to file"""
    with open(output_file, 'w', encoding='utf-8') as file:
        json.dump(cleaned_data, file, indent=2, ensure_ascii=False)



if __name__ == "__main__":
    from get_pdf_superscripts import get_all_superscripts
    # Input JSON file
    sample_json = r"C:\Users\<USER>\Desktop\final_code_v2\extracted_data_folder\04760587J.article.002\extracted_data_segmented.json"
    pdf_path = r"C:\Users\<USER>\Desktop\final_code_v2\pdf_folder\04760587J.article.002.pdf"
    #  
    # superscripts_list = ['[1-2]','[18]', '[2]', '[27]', '[8]', '[17]', '[32]', '[24]', '[12]', '[13–15]', '[6–7]', '[20]', '[4]', '[22]', '[3]', '[26]', '[11]', '[33]', '[1]', '[13]', '[5]', '[15]', '[14]', '[26–27]', '[28]', '[23]', '[16]', '[34]', '[21]', '[19]', '[6]', '[31]', '[10]', '[7]', '[30]', '[25]', '[1–2]', '[29]', '[3–4]', '[9]']

    # superscripts_list =  ['[1-2]','[3-4]','[5]', '[6-7]']
    # superscripts_list =[]
    superscripts_list = get_all_superscripts(pdf_path)
    # print(f"{superscripts_list =}")
    if len(superscripts_list) ==0:
        # citations from the JSON file
        detected_citations = extract_citations_from_json(sample_json)
        superscripts_list = detected_citations 
        print(f"Detected citations: {detected_citations}")
        print(f"{len(detected_citations ) =}")

    # Process the JSON file
    cleaned_json = remove_citation_sentences_from_json(sample_json, superscripts_list)
    
    if cleaned_json:
        # Save the cleaned JSON to a new file
        output_file = sample_json.replace(".json", "_no_citation1.json")
        save_cleaned_json(cleaned_json, output_file)
        print(f"Cleaned JSON saved to: {output_file}")
        
        ## Testing code (Remove or Comment out in Production..)##
        def count_text_fields(data):
            count = 0
            def count_in_section(section_data):
                nonlocal count
                if isinstance(section_data, dict):
                    for key, value in section_data.items():
                        if key == "text" and isinstance(value, str):
                            count += 1
                        elif isinstance(value, (dict, list)):
                            count_in_section(value)
                elif isinstance(section_data, list):
                    for item in section_data:
                        count_in_section(item)
            count_in_section(data)
            return count
        
        text_fields_processed = count_text_fields(cleaned_json)
        print(f"Total text fields processed: {text_fields_processed}")
        #############test ##########
    else:
        print("Failed to process the JSON file.")
