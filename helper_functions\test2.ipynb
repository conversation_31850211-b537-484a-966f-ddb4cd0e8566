import re


def extract_table_and_notes(data):
    """
    Extract table data and exclude non-table lines dynamically.

    Args:
        data (str): Input data as a string containing table and non-table content.

    Returns:
        dict: Dictionary with "table_data" and "notes" (lines outside the table).
    """
    lines = data.split("\n")
    table_lines = []
    notes = []
    inside_table = False

    # Define patterns for table-like lines and non-table-like lines
    table_line_pattern = re.compile(r"[\d\w\(\)\[\];,:=]+")  # Matches table-style lines
    non_table_sentence_pattern = re.compile(
        r"^[A-Z][\w\s,;:()]+[.?!]?$"
    )  # Matches non-table sentence-like lines

    for line in lines:
        line = line.strip()

        # Start detecting table data based on keywords like "TABLE" or similar
        if not inside_table and re.match(r"^TABLE\s*\d*.*", line, re.IGNORECASE):
            inside_table = True
            table_lines.append(line)
            continue

        # If inside table, decide whether to include or exclude the line
        if inside_table:
            if table_line_pattern.match(line) and not non_table_sentence_pattern.match(line):
                # Line looks like table data
                table_lines.append(line)
            else:
                # Line looks like a note, stop collecting table data
                notes.append(line)

    return {
        "table_data": "\n".join(table_lines).strip(),
        "notes": "\n".join(notes).strip(),
    }


# Example Data
data = """TABLE 1. Demographics and Baseline Characteristics
Patient Characteristics
No. of patients
Age in years at baseline
(median [IQR])
Group 0 (n = 8)
Group 1 (n = 11)
Group 2 (n = 34)
Follow-up time in months
(median [IQR])
Injections per patient
(median [IQR])
Total number of injections OD
OS
53 (33 women, 20 men)
37.9 [30.3;47.5]
47.5 [30.3;50.4]
51.8 [46.9;56.6]
77 [49;126]
28.0 [9.8;43.5]
216
266
106 eyes
Treatment group at the end of the
observation period
= no injections = 15.1%
= unilateral injections = 20.8%
= bilateral injections = 64.2%
Abbreviations: IQR = interquartile range OD = oculus dexter; OS = oculus sinister.

predictive value for the occurrence of CNV in the fellow
some more extra extra extra words as a line to be kept outside of table data"""

# Extract table data and notes
result = extract_table_and_notes(data)

print("Table Data:\n", result["table_data"])
print("\nNotes:\n", result["notes"])


import re

def extract_table_data(data):
    """
    Extracts table data dynamically and determines when to stop based on patterns.

    Args:
        data (str): Input text containing table and non-table content.

    Returns:
        str: Extracted table data as a string.
    """
    lines = data.split("\n")
    table_data = []
    inside_table = False

    # Define patterns for table and non-table content
    table_start_pattern = re.compile(r"^TABLE\s*\d*.*", re.IGNORECASE)  # Detects table header
    table_line_pattern = re.compile(r"^[A-Za-z0-9\(\)\[\];,:%=\.\-\s]+$")  # Detects structured table lines
    stop_line_pattern = re.compile(r"^[A-Z][\w\s,;:()]+[.?!]$")  # Detects full sentences (descriptive)

    for line in lines:
        line = line.strip()

        # Start collecting table data if we detect a table header
        if not inside_table and table_start_pattern.match(line):
            inside_table = True
            table_data.append(line)
            continue

        # If inside the table, decide whether to keep the line or stop
        if inside_table:
            if table_line_pattern.match(line):
                # Line looks like table content, add it to the table
                table_data.append(line)
            else:
                # Check for stopping conditions
                if stop_line_pattern.match(line) or line == "":
                    # Line is descriptive or empty, stop collecting
                    break
                else:
                    # Line may still be part of the table, keep collecting
                    table_data.append(line)

    return "\n".join(table_data).strip()


# Example Data
data = """TABLE 1. Demographics and Baseline Characteristics
Patient Characteristics
No. of patients
Age in years at baseline
(median [IQR])
Group 0 (n = 8)
Group 1 (n = 11)
Group 2 (n = 34)
Follow-up time in months
(median [IQR])
Injections per patient
(median [IQR])
Total number of injections OD
OS
53 (33 women, 20 men)
37.9 [30.3;47.5]
47.5 [30.3;50.4]
51.8 [46.9;56.6]
77 [49;126]
28.0 [9.8;43.5]
216
266
106 eyes
Treatment group at the end of the
observation period
= no injections = 15.1%
= unilateral injections = 20.8%
= bilateral injections = 64.2%
Abbreviations: IQR = interquartile range OD = oculus dexter; OS = oculus sinister.
predictive value for the occurrence of CNV in the fellow




"""

# Extract table data
table_data = extract_table_data(data)

print("Table Data:\n", table_data)


import layoutparser as lp
import fitz  # PyMuPDF
import pytesseract
import cv2
import pandas as pd
from io import BytesIO
import numpy as np

def extract_pdf_table(pdf_path, output_csv_path):
    # Load the pre-trained table detection model
    model = lp.Detectron2LayoutModel(
        'lp://PubLayNet/table',
        extra_config={"MODEL.ROI_HEADS.SCORE_THRESH_TEST": 0.85},
        label_map={0: "Table"}
    )

    # Open the PDF
    pdf_document = fitz.open(pdf_path)

    table_data = []  # To store extracted table data

    for page_num in range(len(pdf_document)):
        page = pdf_document[page_num]
        pix = page.get_pixmap()  # Convert PDF page to image
        img = np.frombuffer(pix.samples, dtype=np.uint8).reshape(pix.height, pix.width, pix.n)

        # Convert to OpenCV format
        img_cv = cv2.cvtColor(img, cv2.COLOR_RGB2BGR)

        # Detect tables using the model
        layout = model.detect(img_cv)
        tables = [block for block in layout if block.type == "Table"]

        for table in tables:
            # Crop table region
            x1, y1, x2, y2 = map(int, table.coordinates)
            cropped_table = img_cv[y1:y2, x1:x2]

            # Convert cropped image to grayscale for OCR
            cropped_gray = cv2.cvtColor(cropped_table, cv2.COLOR_BGR2GRAY)

            # Perform OCR on the cropped table
            table_text = pytesseract.image_to_string(cropped_gray, config="--psm 6")

            # Clean and structure table data
            table_lines = table_text.strip().split("\n")
            structured_table = [line.split("\t") for line in table_lines if line.strip()]
            table_data.extend(structured_table)

    # Save the table data to CSV
    if table_data:
        df = pd.DataFrame(table_data)
        df.to_csv(output_csv_path, index=False, header=False)
        print(f"Table data extracted and saved to {output_csv_path}")
    else:
        print("No table data found in the PDF.")


pdf_path =  r"\\***********\bio-act-curation\MAC-Projects\Integrated-Indexing\shipments\982110\06000703B\06000703B.article.002.pdf"  
output_csv_path = "output_table_data.csv" 
extract_pdf_table(pdf_path, output_csv_path)


%pip install layoutparser PyMuPDF opencv-python-headless pytesseract


%pip install layoutparser[detectron2]


# For PyTorch 1.10 and CUDA 11.3
%pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu113
%pip install git+https://github.com/facebookresearch/detectron2.git


!python --version

import layoutparser as lp
from detectron2.engine import DefaultPredictor

print(lp.Detectron2LayoutModel)  # Should not raise an AttributeError




!python.exe -m pip install --upgrade pip

print("Hi")

!python --version

%pip install spacy 
%pip install spacy-layout
!python -m spacy download en_core_web_sm

# %pip install spacy 

%pip install spacy


%pip list


%pip uninstall pillow


%pip install pillow


from PIL import Image


import spacy
# Create a blank spaCy Pipeline for English
nlp = spacy.load("en_core_web_sm")

import spacy
from spacy_layout import spaCyLayout

def extract_tables_from_pdf(pdf_file):
    """
    Extracts table data from the given PDF file using spaCyLayout.

    Args:
        pdf_file (str): Path to the PDF file.

    Returns:
        list: A list of extracted tables, where each table is represented as a list of rows (list of strings).
    """
    # Load the spaCy pipeline
    nlp = spacy.load("en_core_web_sm")

    # Create an instance of spaCyLayout
    layout = spaCyLayout(nlp)

    # Process the PDF file to create a spaCy Doc object
    doc = layout(pdf_file)

    # Retrieve tables in the document
    tables = doc._.tables

    # Extract text for each table
    extracted_tables = []
    for i, table in enumerate(tables):
        table_data = []
        for row in table:
            row_text = [cell.text for cell in row]  # Extract text from each cell in the row
            table_data.append(row_text)
        extracted_tables.append(table_data)
    
    return extracted_tables



pdf_file = pdf_file = r"\\***********\bio-act-curation\MAC-Projects\Integrated-Indexing\shipments\982110\06000703B\06000703B.article.002.pdf"

# Extract tables from the PDF
tables = extract_tables_from_pdf(pdf_file)

# Print extracted tables
for i, table in enumerate(tables):
    print(f"\nTable {i + 1}:")
    for row in table:
        print(" | ".join(row))






import re

def remove_citation_sentences(text, superscripts_list):
    # Join citations as regex pattern (escape special chars)
    citation_pattern = '|'.join(re.escape(c) for c in superscripts_list)
    
    # Split text into sentences by punctuation (including newline, spaces after)
    # This regex splits on period, question mark, or exclamation point
    sentences = re.split(r'(?<=[.?!])\s+', text)
    
    filtered_sentences = []
    for sent in sentences:
        # If any citation from list is present in sentence, skip it
        if re.search(citation_pattern, sent):
            continue
        # Else keep sentence
        filtered_sentences.append(sent.strip())
    
    # Join remaining sentences with space
    return ' '.join(filtered_sentences)


text = ("Prostate  cancer  is  a  male  tumor  that  often  occurs  in  the  urinary  system, with  high  morbidity  and  mortality. [1-2] "
        "The  pathogenic  factors  of  prostate cancer  have  not  been  fully  elucidated,  there  are  studies  showing  that  the "
        "incidence  of  prostate  cancer  is  related  to  genetics,  environment,  sexual activity,  and  eating  habits. [3-4] "
        "Prostate  cancer  patients  can  be  effectively treated  with  excision  surgery,  radiation  therapy,  and  hormone  therapy  at this  stage. "
        "However,  it  has  been  reported  that  60%  of  prostate  cancer patients  will  experience  biochemical  recurrence  after  radical  prostatectomy. [5] "
        "It  is  meaningful  to  monitor  the  prognostic  effect  of  patients  by molecular  means  to  study  more  reliable  prognostic  biomarkers.")

# Citations to detect and remove sentences containing them
superscripts_list = ['[1-2]', '[3-4]', '[5]']

result = remove_citation_sentences(text, superscripts_list)
print(result)


Prostate  cancer  is  a  male  tumor  that  often  occurs  in  the  urinary  system, with  high  morbidity  and  mortality. However,  it  has  been  reported  that  60%  of  prostate  cancer patients  will  experience  biochemical  recurrence  after  radical  prostatectomy.

import re

def remove_citation_sentences(text, superscripts_list):
    # Join citations pattern
    citation_pattern = '|'.join(re.escape(c) for c in superscripts_list)
    
    # Regex to split sentences by punctuation .?! including possible citations at start
    sentence_split_pattern = re.compile(
        rf'(?:[^.!?]*[.!?]+(?:\s+|$)|\s*({citation_pattern})\s*[^.!?]*[.!?]+|\s*({citation_pattern})\s*[^.!?]+$)'
    )
    
    # Find all sentences matching pattern
    sentences = [s for s in sentence_split_pattern.findall(text)]
    
    # If above regex returns empty list, fallback to naive split by punctuation + space
    if not sentences:
        sentences = re.split(r'(?<=[.!?])\s+', text)
    
    # A better approach is to split by punctuation and keep punctuation, including citations at start
    # Alternative: Use a custom split to keep citations attached to sentence
    # Let's use this simpler fallback:
    sentences = re.split(r'(?<=[.!?])\s+', text)
    
    # Prepare to mark sentences to drop
    drop_indices = set()
    
    # For easier checks, join superscripts into a regex pattern
    citation_re = re.compile(r'(' + citation_pattern + r')')
    
    # Function to check if period within 2 spaces before citation in sentence
    def has_period_before_citation(sentence, citation):
        for m in re.finditer(re.escape(citation), sentence):
            start = m.start()
            before = sentence[max(0, start-5):start]  # 3 chars before
            if re.search(r'\.\s{0,5}$', before):
                return True
        return False

    # Function to check if period within 2 spaces after citation in sentence
    def has_period_after_citation(sentence, citation):
        for m in re.finditer(re.escape(citation), sentence):
            end = m.end()
            after = sentence[end:end+5]
            if re.match(r'^\s{0,5}\.', after):
                return True
        return False
    
    # Iterate over sentences to find those with citations and decide which to drop
    for i, sent in enumerate(sentences):
        citations_in_sent = citation_re.findall(sent)
        if not citations_in_sent:
            continue  # no citation, keep
        
        drop_this_sentence = False
        drop_prev_sentence = False
        
        for cit in citations_in_sent:
            if has_period_after_citation(sent, cit):
                # Rule 1: citation followed by period (within citation)
                drop_this_sentence = True
            elif has_period_before_citation(sent, cit):
                # Rule 2: period before citation (outside citation)
                # drop previous sentence if exists
                drop_prev_sentence = True
            else:
                # Rule 3: citation with no period near it (in between citation)
                drop_this_sentence = True
        
        if drop_this_sentence:
            drop_indices.add(i)
        if drop_prev_sentence and i > 0:
            drop_indices.add(i - 1)
    
    # Rebuild text without dropped sentences
    filtered_sentences = [s for i,s in enumerate(sentences) if i not in drop_indices]
    return ' '.join(filtered_sentences)



text = "Prostate  cancer  is  a  male  tumor  that  often  occurs  in  the  urinary  system, with  high  morbidity  and  mortality. [1-2] The  pathogenic  factors  of  prostate cancer  have  not  been  fully  elucidated,  there  are  studies  showing  that  the incidence  of  prostate  cancer  is  related  to  genetics,  environment,  sexual activity,  and  eating  habits. [3-4] Prostate  cancer  patients  can  be  effectively treated  with  excision  surgery,  radiation  therapy,  and  hormone  therapy  at this  stage.  However,  it  has  been  reported  that  60%  of  prostate  cancer patients  will  experience  biochemical  recurrence  after  radical  prostatectomy. [5] It  is  meaningful  to  monitor  the  prognostic  effect  of  patients  by molecular  means  to  study  more  reliable  prognostic  biomarkers."

superscripts_list = ['[1-2]', '[3-4]', '[5]']

result = remove_citation_sentences(text, superscripts_list)
print(result)


import re

def remove_citation_sentences(text, citations):
    # Build regex pattern to match any citation
    citation_pattern = '|'.join(re.escape(c) for c in citations)
    
    # Split text into sentences keeping punctuation
    sentences = re.split(r'(?<=[.!?])\s+', text)
    
    # Find the index of the last sentence containing a citation
    last_citation_idx = -1
    for i, sentence in enumerate(sentences):
        if re.search(citation_pattern, sentence):
            last_citation_idx = i
    
    # If no citations found, return original text
    if last_citation_idx == -1:
        return text.strip()
    
    # Keep only sentences after last citation sentence
    filtered_sentences = sentences[last_citation_idx+1:]
    
    # Remove any sentences with citations that might occur after last citation (if any)
    filtered_sentences = [s for s in filtered_sentences if not re.search(citation_pattern, s)]
    
    return ' '.join(filtered_sentences).strip()


text = ("Prostate  cancer  is  a  male  tumor  that  often  occurs  in  the  urinary  system, with  high  morbidity  and  mortality.[1-2] "
        "The  pathogenic  factors  of  prostate cancer  have  not  been  fully  elucidated,  there  are  studies  showing  that  the "
        "incidence  of  prostate  cancer  is  related  to  genetics,  environment,  sexual activity,  and  eating  habits.[3-4] "
        "Prostate  cancer  patients  can  be  effectively treated  with  excision  surgery,  radiation  therapy,  and  hormone  therapy  at this  stage. "
        "However,  it  has  been  reported  that  60%  of  prostate  cancer patients  will  experience  biochemical  recurrence  after  radical  prostatectomy.[5] "
        "It  is  meaningful  to  monitor  the  prognostic  effect  of  patients  by molecular  means  to  study  more  reliable  prognostic  biomarkers.")

citations = ['[1-2]', '[3-4]', '[5]']

result = remove_citation_sentences(text, citations)
print(result)


import re

def remove_citation_sentences(text, superscripts_list):
    # Join citations pattern
    citation_pattern = '|'.join(re.escape(c) for c in superscripts_list)
    
    # Split sentences by punctuation (.!?), keep punctuation
    # This naive split is usually enough given your example text
    sentences = re.split(r'(?<=[.!?])\s+', text)

    # Regex to find citations in sentence
    citation_re = re.compile(citation_pattern)

    def has_period_before_citation(sentence, citation):
        for m in re.finditer(re.escape(citation), sentence):
            start = m.start()
            # Up to 2 spaces before citation start
            before = sentence[max(0, start-3):start]
            # Check if period '.' occurs immediately before citation within 2 spaces
            if re.search(r'\.\s{0,2}$', before):
                return True
        return False

    def has_period_after_citation(sentence, citation):
        for m in re.finditer(re.escape(citation), sentence):
            end = m.end()
            # Up to 2 spaces after citation end
            after = sentence[end:end+3]
            # Check if period '.' occurs immediately after citation within 2 spaces
            if re.match(r'^\s{0,2}\.', after):
                return True
        return False

    drop_indices = set()
    
    # Iterate sentences and check rules
    for i, sent in enumerate(sentences):
        citations_in_sent = citation_re.findall(sent)
        if not citations_in_sent:
            continue  # no citation, keep sentence

        drop_this_sentence = False
        drop_prev_sentence = False
        
        # Check all citations in sentence
        for cit in citations_in_sent:
            period_before = has_period_before_citation(sent, cit)
            period_after = has_period_after_citation(sent, cit)
            
            if period_after:
                # Rule 1: within citation - drop current sentence
                drop_this_sentence = True
            elif period_before:
                # Rule 2: outside citation - drop previous sentence if exists
                drop_prev_sentence = True
            else:
                # Rule 3: in between citation - drop current sentence
                drop_this_sentence = True
        
        if drop_this_sentence:
            drop_indices.add(i)
        if drop_prev_sentence and i > 0:
            drop_indices.add(i-1)

    # Rebuild filtered text
    filtered_sentences = [sentences[i] for i in range(len(sentences)) if i not in drop_indices]

    return ' '.join(filtered_sentences).strip()


text = "Prostate  cancer  is  a  male  tumor  that  often  occurs  in  the  urinary  system, with  high  morbidity  and  mortality. [1-2] The  pathogenic  factors  of  prostate cancer  have  not  been  fully  elucidated,  there  are  studies  showing  that  the incidence  of  prostate  cancer  is  related  to  genetics,  environment,  sexual activity,  and  eating  habits. [3-4] Prostate  cancer  patients  can  be  effectively treated  with  excision  surgery,  radiation  therapy,  and  hormone  therapy  at this  stage.  However,  it  has  been  reported  that  60%  of  prostate  cancer patients  will  experience  biochemical  recurrence  after  radical  prostatectomy. [5] It  is  meaningful  to  monitor  the  prognostic  effect  of  patients  by molecular  means  to  study  more  reliable  prognostic  biomarkers."
superscripts_list = ['[1-2]', '[3-4]', '[5]']

result = remove_citation_sentences(text, superscripts_list)
print(result)


import re

def split_text_by_citations_and_sentences(text, superscripts_list):
    # Prepare citation pattern
    citation_pattern = '|'.join(re.escape(c) for c in superscripts_list)
    
    # Step 1: Split by citations, keep citations as tokens
    split_by_citation = re.split(f'({citation_pattern})', text)
    # This creates a list where citations are standalone items
    
    final_tokens = []
    # Step 2: For each chunk, if it's citation, add as-is; else split by sentence punctuation
    sentence_end_re = re.compile(r'(?<=[.!?])\s+')
    
    for chunk in split_by_citation:
        if not chunk:
            continue
        # If chunk is a citation token exactly, keep as is
        if re.fullmatch(citation_pattern, chunk):
            final_tokens.append(chunk.strip())
        else:
            # Split this chunk by sentence-ending punctuation + spaces
            sentences = sentence_end_re.split(chunk)
            # Strip sentences and add non-empty ones
            for s in sentences:
                s = s.strip()
                if s:
                    final_tokens.append(s)
    
    return final_tokens

text = "Prostate  cancer  is  a  male  tumor  that  often  occurs  in  the  urinary  system, with  high  morbidity  and  mortality. [1-2] The  pathogenic  factors  of  prostate cancer  have  not  been  fully  elucidated,  there  are  studies  showing  that  the incidence  of  prostate  cancer  is  related  to  genetics,  environment,  sexual activity,  and  eating  habits. [3-4] Prostate  cancer  patients  can  be  effectively treated  with  excision  surgery,  radiation  therapy,  and  hormone  therapy  at this  stage.  However,  it  has  been  reported  that  60%  of  prostate  cancer patients  will  experience  biochemical  recurrence  after  radical  prostatectomy. [5] It  is  meaningful  to  monitor  the  prognostic  effect  of  patients  by molecular [6]  means  to  study  more  reliable  prognostic  biomarkers."

superscripts_list = ['[1-2]', '[3-4]', '[5]','[6]']

tokens = split_text_by_citations_and_sentences(text, superscripts_list)

print("Split tokens:")
for i, t in enumerate(tokens):
    print(f"{i}: {repr(t)}")


import re

def remove_citation_sentences(text, superscripts_list):
    # Join citations pattern
    citation_pattern = '|'.join(re.escape(c) for c in superscripts_list)

    # Split sentences using punctuation + space, retain punctuation
    sentences = re.split(r'(?<=[.!?])\s+', text)

    # Compile citation regex
    citation_re = re.compile(citation_pattern)

    def has_period_before_citation(sentence, citation):
        for m in re.finditer(re.escape(citation), sentence):
            start = m.start()
            before = sentence[max(0, start-3):start]
            if re.search(r'\.\s{0,2}$', before):
                return True
        return False

    def has_period_after_citation(sentence, citation):
        for m in re.finditer(re.escape(citation), sentence):
            end = m.end()
            after = sentence[end:end+3]
            if re.match(r'^\s{0,2}\.', after):
                return True
        return False

    drop_indices = set()

    for i, sent in enumerate(sentences):
        citations_in_sent = citation_re.findall(sent)
        if not citations_in_sent:
            continue  # keep sentence

        drop_this_sentence = False
        drop_prev_sentence = False

        for cit in citations_in_sent:
            if has_period_before_citation(sent, cit):
                # Outside citation (e.g., ". [1]") → drop previous sentence
                drop_prev_sentence = True
            elif has_period_after_citation(sent, cit):
                # Within citation (e.g., "[1].") → drop current sentence
                drop_this_sentence = True
            # In-between citations → DO NOTHING

        if drop_this_sentence:
            drop_indices.add(i)
        if drop_prev_sentence and i > 0:
            drop_indices.add(i - 1)

    # Rebuild filtered text
    filtered_sentences = [sentences[i] for i in range(len(sentences)) if i not in drop_indices]

    return ' '.join(filtered_sentences).strip()



text = "Prostate  cancer  is  a  male  tumor  that  often  occurs  in  the  urinary  system, with  high  morbidity  and  mortality. [1-2] The  pathogenic  factors  of  prostate cancer  have  not  been  fully  elucidated,  there  are  studies  showing  that  the incidence  of  prostate  cancer  is  related  to  genetics,  environment,  sexual activity,  and  eating  habits. [3-4] Prostate  cancer  patients  can  be  effectively treated  with  excision  surgery,  radiation  therapy,  and  hormone  therapy  at this  stage.  However,  it  has  been  reported  that  60%  of  prostate  cancer patients  will  experience  biochemical  recurrence  after  radical  prostatectomy. [5] It  is  meaningful  to  monitor  the  prognostic  effect  of  patients  by molecular  means  to  study  more  reliable  prognostic  biomarkers."
superscripts_list = ['[1-2]', '[3-4]', '[5]']

result = remove_citation_sentences(text, superscripts_list)
print("Filtered Text:\n", result)


import re

def remove_citation_sentences(text, superscripts_list):
    # Join citation pattern
    citation_pattern = '|'.join(re.escape(c) for c in superscripts_list)
    citation_re = re.compile(citation_pattern)

    # Sentence split
    sentences = re.split(r'(?<=[.!?])\s+', text)

    drop_indices = set()

    for i, sent in enumerate(sentences):
        # Trim leading space for citation check
        trimmed = sent.strip()

        # Rule: outside-citation → sentence starts with citation (sentence.[1])
        if citation_re.match(trimmed) and i > 0:
            drop_indices.add(i - 1)
            continue

        # Rule: within-citation → citation followed by period in same sentence (senteence [2])
        citations_in_sent = citation_re.findall(sent)
        for cit in citations_in_sent:
            for m in re.finditer(re.escape(cit), sent):
                end = m.end()
                after = sent[end:end+3]
                if re.match(r'^\s{0,2}\.', after):
                    drop_indices.add(i)
                    break

        # Rule: in-between-citation → citation not followed or preceded by period in sentence (sentennce [1] text)

    # Rebuild filtered text
    filtered_sentences = [sentences[i] for i in range(len(sentences)) if i not in drop_indices]

    return ' '.join(filtered_sentences).strip()



text = "Prostate  cancer  is  a  male  tumor  that  often  occurs  in  the  urinary  system, with  high  morbidity  and  mortality. [1-2] The  pathogenic  factors  of  prostate cancer  have  not  been  fully  elucidated,  there  are  studies  showing  that  the incidence  of  prostate  cancer  is  related  to  genetics,  environment,  sexual activity,  and  eating  habits. [3-4] Prostate  cancer  patients  can  be  effectively treated  with  excision  surgery,  radiation  therapy,  and  hormone  therapy  at this  stage.  However,  it  has  been  reported  that  60%  of  prostate  cancer patients  will  experience  biochemical  recurrence  after  radical  prostatectomy. [5] It  is  meaningful  to  monitor  the  prognostic  effect  of  patients  by molecular [6] means  to  study  more  reliable  prognostic  biomarkers."
superscripts_list = ['[1-2]', '[3-4]', '[5]', '[6]']

result = remove_citation_sentences(text, superscripts_list)
print("Filtered Text:\n", result)


#  [3-4] Prostate  cancer  patients  can  be  effectively treated  with  excision  surgery,  radiation  therapy,  and  hormone  therapy  at this  stage. [5] It  is  meaningful  to  monitor  the  prognostic  effect  of  patients  by molecular [6] means  to  study  more  reliable  prognostic  biomarkers.

import re

def remove_citation_sentences(text, superscripts_list):
    # Join citation pattern
    citation_pattern = '|'.join(re.escape(c) for c in superscripts_list)
    citation_re = re.compile(citation_pattern)

    # Sentence split
    sentences = re.split(r'(?<=[.!?])\s+', text)

    drop_indices = set()

    for i, sent in enumerate(sentences):
        # Trim leading space for citation check
        trimmed = sent.strip()

        # Rule: outside-citation → sentence starts with citation (sentence.[1])
        if citation_re.match(trimmed) and i > 0:
            drop_indices.add(i - 1)
            continue

        # Rule: within-citation → citation followed by period in same sentence (senteence [2])
        citations_in_sent = citation_re.findall(sent)
        for cit in citations_in_sent:
            for m in re.finditer(re.escape(cit), sent):
                end = m.end()
                after = sent[end:end+3]
                if re.match(r'^\s{0,2}\.', after):
                    drop_indices.add(i)
                    break

        # Rule: in-between-citation → citation not followed or preceded by period in sentence (sentennce [1] text)

    # Rebuild filtered text and remove citation markers
    filtered_sentences = []
    for i in range(len(sentences)):
        if i not in drop_indices:
            # Remove citation markers from the sentence
            clean_sentence = citation_re.sub('', sentences[i])
            # Clean up extra spaces that might result from citation removal
            clean_sentence = re.sub(r'\s+', ' ', clean_sentence).strip()
            if clean_sentence:  # Only add non-empty sentences
                filtered_sentences.append(clean_sentence)

    return ' '.join(filtered_sentences).strip()

text = "Prostate  cancer  is  a  male  tumor  that  often  occurs  in  the  urinary  system, with  high  morbidity  and  mortality. [1-2] The  pathogenic  factors  of  prostate cancer  have  not  been  fully  elucidated,  there  are  studies  showing  that  the incidence  of  prostate  cancer  is  related  to  genetics,  environment,  sexual activity,  and  eating  habits. [3-4] Prostate  cancer  patients  can  be  effectively treated  with  excision  surgery,  radiation  therapy,  and  hormone  therapy  at this  stage.  However,  it  has  been  reported  that  60%  of  prostate  cancer patients  will  experience  biochemical  recurrence  after  radical  prostatectomy. [5] It  is  meaningful  to  monitor  the  prognostic  effect  of  patients  by molecular [6] means  to  study  more  reliable  prognostic  biomarkers."
superscripts_list = ['[1-2]', '[3-4]', '[5]', '[6]']

result = remove_citation_sentences(text, superscripts_list)
print("Filtered Text:\n", result)


 Prostate cancer patients can be effectively treated with excision surgery, radiation therapy, and hormone therapy at this stage. It is meaningful to monitor the prognostic effect of patients by molecular means to study more reliable prognostic biomarkers.

import re
import json

def remove_citation_sentences_from_json(json_file_path, superscripts_list):
    """
    Remove citation sentences from JSON file and clean citation markers from all text fields.
    
    Args:
        json_file_path (str): Path to the JSON file
        superscripts_list (list): List of citation patterns to remove
    
    Returns:
        dict: Updated JSON structure with cleaned text
    """
    # Load the JSON file
    try:
        with open(json_file_path, 'r', encoding='utf-8') as file:
            data = json.load(file)
    except FileNotFoundError:
        print(f"Error: File not found at {json_file_path}")
        return None
    except json.JSONDecodeError:
        print(f"Error: Invalid JSON format in {json_file_path}")
        return None
    
    # Join citation pattern
    citation_pattern = '|'.join(re.escape(c) for c in superscripts_list)
    citation_re = re.compile(citation_pattern)
    
    def clean_text(text):
        """Clean individual text content"""
        if not text or not isinstance(text, str):
            return text
            
        # Sentence split
        sentences = re.split(r'(?<=[.!?])\s+', text)
        drop_indices = set()

        for i, sent in enumerate(sentences):
            # Trim leading space for citation check
            trimmed = sent.strip()

            # Rule: outside-citation → sentence starts with citation (sentence.[1])
            if citation_re.match(trimmed) and i > 0:
                drop_indices.add(i - 1)
                continue

            # Rule: within-citation → citation followed by period in same sentence OR at end of sentence
            citations_in_sent = citation_re.findall(sent)
            for cit in citations_in_sent:
                for m in re.finditer(re.escape(cit), sent):
                    end = m.end()
                    after = sent[end:end+3]
                    # Check if citation is followed by period OR at the end of sentence
                    if re.match(r'^\s{0,2}\.', after) or end >= len(sent.strip()):
                        drop_indices.add(i)
                        break

            # Additional rule: Check if sentence ends with citation (even without period)
            if citations_in_sent:
                for cit in citations_in_sent:
                    # Create pattern to match citation at end of sentence (with optional whitespace)
                    end_pattern = re.escape(cit) + r'\s*$'
                    if re.search(end_pattern, sent):
                        drop_indices.add(i)
                        break

        # Rebuild filtered text and remove citation markers
        filtered_sentences = []
        for i in range(len(sentences)):
            if i not in drop_indices:
                # Remove citation markers from the sentence
                clean_sentence = citation_re.sub('', sentences[i])
                # Clean up extra spaces that might result from citation removal
                clean_sentence = re.sub(r'\s+', ' ', clean_sentence).strip()
                if clean_sentence:  # Only add non-empty sentences
                    filtered_sentences.append(clean_sentence)

        return ' '.join(filtered_sentences).strip()
    
    def process_section(section_data):
        """Recursively process section data to find and clean text fields"""
        if isinstance(section_data, dict):
            processed_data = {}
            for key, value in section_data.items():
                if key == "text" and isinstance(value, str):
                    # Clean the text content
                    processed_data[key] = clean_text(value)
                elif isinstance(value, (dict, list)):
                    # Recursively process nested structures
                    processed_data[key] = process_section(value)
                else:
                    # Keep other fields as is
                    processed_data[key] = value
            return processed_data
        elif isinstance(section_data, list):
            return [process_section(item) for item in section_data]
        else:
            return section_data
    
    # Process the entire JSON structure
    cleaned_data = process_section(data)
    return cleaned_data

def save_cleaned_json(cleaned_data, output_file):
    """Save cleaned JSON data to file"""
    with open(output_file, 'w', encoding='utf-8') as file:
        json.dump(cleaned_data, file, indent=2, ensure_ascii=False)

def extract_citations_from_json(json_file_path):
    """
    Extract all citation patterns from the JSON file to automatically detect superscripts.
    
    Args:
        json_file_path (str): Path to the JSON file
    
    Returns:
        list: List of unique citation patterns found in the text
    """
    try:
        with open(json_file_path, 'r', encoding='utf-8') as file:
            data = json.load(file)
    except:
        return []
    
    # Common citation patterns
    citation_patterns = []
    citation_regex = re.compile(r'\[[\d\-,\s]+\]')
    
    def find_citations_in_section(section_data):
        if isinstance(section_data, dict):
            for key, value in section_data.items():
                if key == "text" and isinstance(value, str):
                    citations = citation_regex.findall(value)
                    citation_patterns.extend(citations)
                elif isinstance(value, (dict, list)):
                    find_citations_in_section(value)
        elif isinstance(section_data, list):
            for item in section_data:
                find_citations_in_section(item)
    
    find_citations_in_section(data)
    return list(set(citation_patterns))  # Return unique citations


if __name__ == "__main__":
    # Input JSON file
    sample_json = r"C:\Users\<USER>\Desktop\final_code_v2\extracted_data_folder\04760587J.article.002\extracted_data_segmented.json"
    
    # citations from the JSON file
    # detected_citations = extract_citations_from_json(sample_json)
    # print(f"Detected citations: {detected_citations}")
    

    # superscripts_list = detected_citations  
    superscripts_list = ['[1-2]','[18]', '[2]', '[27]', '[8]', '[17]', '[32]', '[24]', '[12]', '[13–15]', '[6–7]', '[20]', '[4]', '[22]', '[3]', '[26]', '[11]', '[33]', '[1]', '[13]', '[5]', '[15]', '[14]', '[26–27]', '[28]', '[23]', '[16]', '[34]', '[21]', '[19]', '[6]', '[31]', '[10]', '[7]', '[30]', '[25]', '[1–2]', '[29]', '[3–4]', '[9]']

    # superscripts_list =  ['[1-2]','[3-4]','[5]', '[6-7]']
    # Process the JSON file
    cleaned_json = remove_citation_sentences_from_json(sample_json, superscripts_list)
    
    if cleaned_json:
        # Save the cleaned JSON to a new file
        output_file = sample_json.replace(".json", "_no_citation.json")
        save_cleaned_json(cleaned_json, output_file)
        print(f"Cleaned JSON saved to: {output_file}")
        
        ## Testing code (Remove or Comment out in Production..)##
        def count_text_fields(data):
            count = 0
            def count_in_section(section_data):
                nonlocal count
                if isinstance(section_data, dict):
                    for key, value in section_data.items():
                        if key == "text" and isinstance(value, str):
                            count += 1
                        elif isinstance(value, (dict, list)):
                            count_in_section(value)
                elif isinstance(section_data, list):
                    for item in section_data:
                        count_in_section(item)
            count_in_section(data)
            return count
        
        text_fields_processed = count_text_fields(cleaned_json)
        print(f"Total text fields processed: {text_fields_processed}")
        #############test ##########
    else:
        print("Failed to process the JSON file.")


import re
def normalize_superscripts_hyphens(superscripts_list):
    """
    Normalize all different types of hyphens in superscripts list to single hyphen.
    Handles various Unicode hyphens and encoded hyphen representations.
    
    Args:
        superscripts_list (list): List of citation superscripts that may contain various hyphen types
        
    Returns:
        list: Normalized list with all hyphen variants converted to single hyphen
    """
    normalized_superscripts = set()
    
    for s in superscripts_list:
        # Replace all different types of hyphens with single hyphen
        normalized_s = s
        
        # Replace various hyphen types: en dash, em dash, minus sign, figure dash, etc.
        hyphen_variants = ['–', '—', '−', '‒', '‑', '﹣', '－']
        for hyphen in hyphen_variants:
            normalized_s = normalized_s.replace(hyphen, '-')
        
        # Replace encoded hyphens (HTML entities and Unicode escapes)
        encoded_hyphens = ['&ndash;', '&mdash;', '&minus;', '\\u2013', '\\u2014', '\\u2212', '\\u2010', '\\u2011']
        for encoded in encoded_hyphens:
            normalized_s = normalized_s.replace(encoded, '-')
        
        # Add both original and normalized versions
        normalized_superscripts.add(s)
        normalized_superscripts.add(normalized_s)
    
    return list(normalized_superscripts)

def remove_sentences_with_inbetween_citations(text, superscripts_list_raw):
    """
    Removes any sentence that contains a citation from the normalized citation list.
    """
    normalized_citations = normalize_superscripts_hyphens(superscripts_list_raw)

    # Normalize hyphens in the text
    hyphen_variants = ['–', '—', '−', '‒', '‑', '﹣', '－']
    encoded_hyphens = ['&ndash;', '&mdash;', '&minus;', '\\u2013', '\\u2014', '\\u2212', '\\u2010', '\\u2011']

    normalized_text = text
    for hyphen in hyphen_variants + encoded_hyphens:
        normalized_text = normalized_text.replace(hyphen, '-')

    # Split text into sentences using punctuation followed by whitespace
    sentence_pattern = r'(?<=[.!?])\s+'
    sentences = re.split(sentence_pattern, normalized_text)

    # Compile regex pattern to match any citation
    escaped_citations = [re.escape(cite) for cite in normalized_citations]
    citation_pattern = re.compile('|'.join(escaped_citations))

    # Remove sentences containing citations
    cleaned_sentences = [s for s in sentences if not citation_pattern.search(s)]

    return ' '.join(cleaned_sentences)


text = ("Prostate  cancer  is  a  male  tumor  that  often  occurs  in  the  urinary  system, with  high  morbidity  and  mortality. [1-2] The  pathogenic  factors  of  prostate cancer  have  not  been  fully  elucidated,  there  are  studies  showing  that  the incidence  of  prostate  cancer  is  related  to  genetics,  environment,  sexual activity,  and  eating  habits. [3-4] Prostate  cancer  patients  can  be  effectively treated  with  excision  surgery,  radiation  therapy,  and  hormone  therapy  at this  stage.  However,  it  has  been  reported  that  60%  of  prostate  cancer patients  will  experience  biochemical  recurrence  after  radical  prostatectomy. It  is  meaningful  to  monitor  the  prognostic  effect [100] of  patients  by molecular  means  to  study  more  reliable  prognostic  biomarkers.")

superscripts_list = ['[1-2]', '[18]', '[2]', '[27]', '[8]', '[17]', '[32]', '[24]', '[12]', '[13–15]', '[6–7]',
                     '[20]', '[4]', '[22]', '[3]', '[26]', '[11]', '[33]', '[1]', '[13]', '[5]', '[15]', '[14]',
                     '[26–27]', '[28]', '[23]', '[16]', '[34]', '[21]', '[19]', '[6]', '[31]', '[10]', '[7]',
                     '[30]', '[25]', '[1–2]', '[29]', '[3–4]', '[9]', '[100]']

cleaned_text = remove_sentences_with_inbetween_citations(text, superscripts_list)
print(f"{cleaned_text=}")


import re

def remove_inbetween_citation_sentences(json_data, superscripts_list):
    """
    Processes each item in json_data, extracts the "text" field, and removes
    sentences that contain any citation from the superscripts_list.

    Args:
        json_data (list): A list of dictionaries with text data under the "text" field.
        superscripts_list (list): A list of citation strings to check for (e.g., '[1]', '[13–15]').

    Returns:
        list: The same structure as json_data but with updated "text" fields where cited sentences are removed.
    """

    # Compile citation regex pattern from the list
    citation_regex = re.compile(r'(' + '|'.join(map(re.escape, superscripts_list)) + r')')

    # Sentence splitting pattern
    sentence_splitter = re.compile(r'(?<=[.!?])\s+')

    cleaned_json = []

    for item in json_data:
        updated_item = {}

        for key, value in item.items():
            if isinstance(value, dict) and "text" in value:
                original_text = value.get("text", "")
                sentences = sentence_splitter.split(original_text)
                filtered_sentences = [s for s in sentences if not citation_regex.search(s)]
                value["text"] = ' '.join(filtered_sentences)
                updated_item[key] = value
            else:
                updated_item[key] = value  # Preserve non-dict fields or non-"text" fields

        cleaned_json.append(updated_item)

    return cleaned_json
json_data = [
    {
        "#/texts/51": {
            "text": "The protein was [1] purified using standard methods . This method has been described previously [13–15]. Our findings demonstrate a novel mechanism.",
            "page_no": 5
        }
    }
]
superscripts_list = ['[1-2]', '[18]', '[2]', '[27]', '[8]', '[17]', '[32]', '[24]', '[12]', '[13–15]', '[6–7]',
                     '[20]', '[4]', '[22]', '[3]', '[26]', '[11]', '[33]', '[1]', '[13]', '[5]', '[15]', '[14]',
                     '[26–27]', '[28]', '[23]', '[16]', '[34]', '[21]', '[19]', '[6]', '[31]', '[10]', '[7]',
                     '[30]', '[25]', '[1–2]', '[29]', '[3–4]', '[9]']

cleaned_data = remove_inbetween_citation_sentences(json_data, superscripts_list)
print(cleaned_data)

import re

def get_aim_of_abstract(abstract_list, abstract_location):
    """
    Extract the aim of the paper from the abstract section by identifying keywords.
    Returns all text from the match onward.

    Args:
        abstract_list (list): List of dictionaries containing text and metadata from the abstract section.
        abstract_location (list): List of page numbers where the abstract is located.

    Returns:
        dict: A dictionary containing the aim of the abstract with metadata preserved.
    """
    section_content = {}
    text_only_list = []

    if isinstance(abstract_list, list):
        for item in abstract_list:
            if isinstance(item, dict) and "text" in item:
                text_only_list.append(item["text"])

    full_text = " ".join(text_only_list)

    # Define keywords and build a regex pattern to ignore line breaks and hyphens
    keywords = [
        "investigates the potential of new approaches",
        "investigates cutting-edge technologies in",
        "focuses on enhancing the capabilities of",
        "focuses on addressing knowledge gaps in",
    ]

    keywords_pattern = r"\b(" + "|".join(keywords) + r")\b"
    regex_pattern = re.compile(
        keywords_pattern.replace(" ", r"(?:[\s\-]*\n?)*"), re.IGNORECASE
    )

    match = regex_pattern.search(full_text)

    if match:
        start_pos = match.start()
        current_pos = 0
        match_item_index = 0

        for i, text in enumerate(text_only_list):
            if current_pos <= start_pos < current_pos + len(text):
                match_item_index = i
                break
            current_pos += len(text) + 1  # +1 for the space added during join

        relative_pos = start_pos - current_pos
        match_item = abstract_list[match_item_index]
        match_text = match_item["text"]

        # Create a modified version of the match item, starting from the matched keyword
        match_item_modified = {
            "text": match_text[relative_pos:].strip(),
            "page_no": match_item.get("page_no", 0),
            "fitz_coords": match_item.get("fitz_coords", {})
        }

        abstract_content = [match_item_modified]

        # Add all items after the match
        for item in abstract_list[match_item_index + 1:]:
            if isinstance(item, dict):
                abstract_content.append({
                    "text": item.get("text", ""),
                    "page_no": item.get("page_no", 0),
                    "fitz_coords": item.get("fitz_coords", {})
                })

        section_content["abstract"] = abstract_content
        section_content["page_number"] = abstract_location
        return section_content

    else:
        # No match found — return the entire abstract
        abstract_content = []
        for item in abstract_list:
            if isinstance(item, dict):
                abstract_content.append({
                    "text": item.get("text", ""),
                    "page_no": item.get("page_no", 0),
                    "fitz_coords": item.get("fitz_coords", {})
                })

        section_content["abstract"] = abstract_content
        section_content["page_number"] = abstract_location
        return section_content


def get_aim_of_abstract(abstract_buffer, keywords=None):
    if keywords is None:
        keywords = [
            "investigates the potential of new approaches",
            "investigates cutting-edge technologies in",
        ]
    
    # Combine all texts into one string to search keywords
    full_text = " ".join([item["text"] for item in abstract_buffer])

    import re

    # Build a simple regex to search keywords
    pattern = "|".join([re.escape(k) for k in keywords])
    regex = re.compile(pattern, re.IGNORECASE)

    if regex.search(full_text):
        # Found keyword - return from keyword onward
        # Find where the match starts and slice from there
        match = regex.search(full_text)
        start_pos = match.start()

        current_pos = 0
        for i, item in enumerate(abstract_buffer):
            text_len = len(item["text"])
            if current_pos <= start_pos < current_pos + text_len:
                # Adjust text from match position
                relative_start = start_pos - current_pos
                new_text = item["text"][relative_start:].lstrip()
                new_abstract_buffer = [ 
                    {**item, "text": new_text} 
                ] + abstract_buffer[i+1:]
                return new_abstract_buffer
            current_pos += text_len + 1

    else:
        # No keyword found - return entire abstract unchanged
        return abstract_buffer


abstract_buffer = [
    {
        'text': 'LncRNAs  are  abnormally  expressed  in  a  variety  of  cancers  and play  unique  roles  in  therapy.  Based  on  this,  the  prognostic value of lncRNA LINC01018 in prostate cancer was discussed in this  study.  LINC01018  was  underexpressed  in  prostate  cancer tissues  and  cells,  while  miR-182-5p  was  elevated  (*** p <  0.001). Overexpression  of  LINC01018  may  inhibit  the  progression  of prostate  cancer  by  targeting  miR-182-5p.  This  study  revealed that upregulated LINC01018 may prolong the overall survival of patients with prostate cancer (log-rank p =  0.042), and LINC01018 may become a prognostic biomarker for patients with prostate cancer, which  brings  a  new  direction  for  the  treatment  of patients.',
        'page_no': 1,
        'fitz_coords': {'x0': 84.0, 'y0': 234.43599999999998, 'x1': 325.938, 'y1': 352.095, 'coord_origin': 'TOPLEFT'}
    },
    {
        'text': 'Received  9  November 2022 Accepted  18  December 2023',
        'page_no': 1,
        'fitz_coords': {'x0': 342.5, 'y0': 233.235, 'x1': 424.824, 'y1': 267.043, 'coord_origin': 'TOPLEFT'}
    }
]

abstract_output = get_aim_of_abstract(abstract_buffer)
print("abstract_output =", abstract_output)


import json
from collections import defaultdict
from docling import Document

def extract_text_with_linebreaks(pdf_path, json_output_path, y_threshold=2.0, paragraph_gap=15):
    """
    Extracts text from PDF using Docling, preserving line breaks and paragraphs.
    
    Args:
        pdf_path (str): Path to input PDF.
        json_output_path (str): Path to save JSON output.
        y_threshold (float): Max y-difference between words in the same line.
        paragraph_gap (float): Min vertical gap to start a new paragraph.
    """
    doc = Document.from_file(pdf_path)
    full_text = ""
    pages_output = []

    for page in doc.pages:
        words = page.words
        if not words:
            continue

        # Group words by approximate y position (line)
        lines_dict = defaultdict(list)
        for word in words:
            y = round(word.bbox[1] / y_threshold) * y_threshold
            lines_dict[y].append(word)

        # Sort lines top to bottom
        sorted_line_ys = sorted(lines_dict.keys())
        prev_y = None
        page_text = ""

        for y in sorted_line_ys:
            line_words = lines_dict[y]
            # Sort words left to right
            sorted_words = sorted(line_words, key=lambda w: w.bbox[0])
            line_text = " ".join([w.text for w in sorted_words])

            # Detect paragraph break based on line spacing
            if prev_y is not None and abs(y - prev_y) > paragraph_gap:
                page_text += "\n\n"  # Paragraph break
            else:
                page_text += "\n"  # Line break 

            page_text += line_text
            prev_y = y

        full_text += page_text + "\n\n"  # Page break as paragraph break

        pages_output.append({
            "page_no": page.page_number,
            "text": page_text.strip()
        })

    # Save to JSON
    output = {
        "text": full_text.strip(), 
        "pages": pages_output
    }

    with open(json_output_path, "w", encoding="utf-8") as f:
        json.dump(output, f, indent=2, ensure_ascii=False)

    print(f"Text with line breaks saved to: {json_output_path}")
    pdf_path = r"C:\Users\<USER>\Desktop\final_code_v2\pdf_folder\04760587J.article.002.pdf"
    extract_text_with_linebreaks(pdf_path, "output.json")

