"""
This module processes JSON files to extract structured content including:
- Abstract
- Introduction and research aims
- Experimental procedures
- Results and discussions
- Conclusions
- Figures and tables

The module uses various helper functions to parse and extract content from different sections
of JSON documents. It processes text content, figures and tables to create a structured 
representation of the document content while preserving all metadata.

Author: Anand Jadhav
Date: 28-05-2025
"""

## Imports required for the module
import re
import time
import logging
import json 
import os

## Custom Imports
from helper_functions.get_introduction import get_aim_of_paper
# from helper_functions.get_abstract_content import get_abstract
from helper_functions.get_abstract_aim import get_aim_of_abstract
# from helper_functions.get_conclusion import get_conclusion
# from helper_functions.get_results import get_results
# from helper_functions.get_experimental_procedures import get_experimental
# from helper_functions.get_results_superscripts import get_results_without_references
# from helper_functions.get_material_methods import get_materials
from helper_functions.get_not_required import get_not_required_content

from helper_functions.coordinate_conversion import convert_bbox_to_fitz
from helper_functions.get_pdf_superscripts import get_all_superscripts
from helper_functions.remove_reference_sents import remove_citation_sentences_from_json

# from helper_functions.get_pdf_superscripts import get_all_superscripts
# from helper_functions.get_image_text import extract_and_clean_text
# from helper_functions.get_figure_titles import extract_figure_paragraphs
# from helper_functions.get_pdf_table_text import extract_tables_with_page_numbers



def create_title_regex(title):
    title = re.escape(title.strip())
    pattern = rf"(?i)\b(?:[0-9A-Z]+\s*[.)]?\s*)?{title}\b"
    return pattern

def get_json_text_main(json_file_path, tan_name, title_list, article_size, research):
    json_content = {}
    total_pages = 0

    # Create mapping from first item in each title group to normalized output key
    title_group_to_key = {
        intro_list[0]: "introduction",
        abstract_list[0]: "abstract",
        keywords_list[0]: "keywords",
        experimental_list[0]: "experimental_procedures",
        results_list[0]: "results",
        materials_methods_list[0]: "materials_methods",
        conclusion_list[0]: "conclusion",
        acknowledgments_list[0]: "acknowledgments",
        references_list[0]: "references",
        not_required_list[0]: "not_required_content"
    }

    try:
        with open(json_file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)

        # Get page heights if available (for fitz bbox conversion)
        page_heights = {}
        for page_num, page_info in data.get("pages", {}).items():
            if isinstance(page_info, dict) and "size" in page_info:
                page_heights[int(page_num)] = page_info["size"].get("height", 0)
        total_pages = len(page_heights)

        # Setup containers
        header_sections = {key: {"section_text": []} for key in title_group_to_key.values()}
        # print(f"{header_sections =}")
        header_sections["pdf_info"] = {"section_text": []}
        header_location = {key: [] for key in title_group_to_key.values()}
        header_location["pdf_info"] = []

        current_matched_key = "pdf_info" 

        # Process each section
        for section in data.get("text_data", []):
            header = section.get("header", "").strip()
            section_texts = section.get("section_text", [])
            matched_section_key = None

            # Check if current header matches any title
            for title_group in title_list:
                for title in title_group:
                    pattern = create_title_regex(title)
                    if re.search(pattern, header, re.IGNORECASE | re.VERBOSE):
                        matched_section_key = title_group_to_key.get(title_group[0], "pdf_info")
                        print(f"{matched_section_key =}")
                        break
                if matched_section_key: 
                    break

            # Update current matched key if a new one is found 
            if matched_section_key:
                current_matched_key = matched_section_key

            # Add header as pseudo-section_text (if it's not empty)
            if header:
                print(f"{header =}")
                pseudo_text = {
                    "text": header,
                    "page_no": section_texts[0][list(section_texts[0].keys())[0]].get("page_no", 0)
                    if section_texts else 0
                }
                header_sections[current_matched_key]["section_text"].append({"header": pseudo_text})
                page_no = pseudo_text["page_no"]
                if page_no not in header_location[current_matched_key]:
                    header_location[current_matched_key].append(page_no)

            # Process actual section text
            for text_item in section_texts:
                for key, value in text_item.items():
                    if isinstance(value, dict):
                        modified_value = {
                            "text": value.get("text", ""),
                            "page_no": value.get("page_no", 0)
                        }
                        page_no = value.get("page_no", 0)
                        bbox = value.get("bbox", {})
                        page_height = page_heights.get(page_no, 0)

                        if page_height and all(k in bbox for k in ("l", "t", "r", "b")):
                            fitz_coords = convert_bbox_to_fitz(bbox, page_height)
                            modified_value["fitz_coords"] = {
                                "x0": fitz_coords[0],
                                "y0": fitz_coords[1],
                                "x1": fitz_coords[2],
                                "y1": fitz_coords[3],
                                "coord_origin": "TOPLEFT"
                            }

                        header_sections[current_matched_key]["section_text"].append({key: modified_value})
                        if page_no not in header_location[current_matched_key]:
                            header_location[current_matched_key].append(page_no)

        # Compile final output
        for section_key, section_data in header_sections.items():
            if section_data["section_text"]:
                json_content[section_key] = {
                    "section_text": section_data["section_text"],
                    "page_numbers": sorted(header_location.get(section_key, []))
                }
        json_content = decode_unicode_in_json(json_content)
        return json_content, total_pages, article_size

    except Exception as error:
        print(f"Error processing JSON: {error}")
        return {}, 0, article_size




def decode_unicode_in_json(data):
    """
    Recursively convert unicode escape sequences (like \\u00b1) to actual characters (like ±)
    in all string values within the nested dictionary or list.
    """
    if isinstance(data, dict):
        return {k: decode_unicode_in_json(v) for k, v in data.items()}
    elif isinstance(data, list):
        return [decode_unicode_in_json(item) for item in data]
    elif isinstance(data, str):
        # Decode unicode sequences
        try:
            return data.encode('utf-8').decode('unicode_escape')
        except UnicodeDecodeError:
            return data  # Return original if decoding fails
    else:
        return data  # Return as-is for non-string values



def save_segmented_json(json_content, output_json_path):
    """
    Save the segmented content to a JSON file.
    
    Args:
        json_content (dict): Dictionary containing the segmented content
        tan_name (str): Name of the TAN document
    
    Returns:
        str: Path to the saved JSON file
    """
    try:
        # Create json_folder if it doesn't exist
        # os.makedirs("json_folder", exist_ok=True)
        
        # Save the segmented data to a JSON file
        # output_json_path = f"json_folder/segmented_{tan_name}.json"
        with open(output_json_path, 'w', encoding='utf-8') as f:
            json.dump(json_content, f, indent=2)
        
        print(f"Segmented data saved to {output_json_path}")
        return output_json_path
    
    except Exception as error:
        logging.error("save_segmented_json Error: %s", str(error)) 
        return None

def get_citationless_segmented_json(pdf_path, json_file_path):
    superscripts_list_raw = get_all_superscripts(pdf_path)
    cleaned_data = remove_citation_sentences_from_json(json_file_path, superscripts_list_raw)
    output_file = json_file_path.replace(".json", "_no_citation.json")
    save_cleaned_json(cleaned_data, output_file)

def save_cleaned_json(cleaned_data, output_file):
    """Save cleaned JSON data to file"""
    with open(output_file, 'w', encoding='utf-8') as file:
        json.dump(cleaned_data, file, indent=2, ensure_ascii=False)
        print(f"Captionless Segmented data saved to {output_file}")

if __name__ == "__main__":
    logging.basicConfig(
        filename="json_segmented_content.log",
        level=logging.ERROR,
        format="%(asctime)s - %(levelname)s: %(message)s",
    )
    start = time.time()

    # Variations for different sections of a document
    intro_list = [
        "INTRODUCTION",
        "I N T R O D U C T I O N",
        "I n t r o d u c t i o n",
        "Introduction",
        "Background",
        "Introduction and Background",
        "B A C K G R O U N D",
    ]
    abstract_list = [
        "ABSTRACT",
        "Abstract",
        "A B S T R A C T",
        "a b s t r a c t",
        "Summary of Findings",
        "Abst.",
        "Summary",
        "S U M M A R Y",
    ]
    keywords_list = [
        "KEYWORDS",
        "Keywords",
        "K E Y W O R D S",
        "Key Terms",
        "Topics",
    ]
    experimental_list = [
        "EXPERIMENTAL PROCEDURES",
        "Experimental section",
        "Experimental Sections",
        "Experimental Section",
        "EXPERIMENTAL SECTION",
        "Experimental Section",
        "Experimental section",
        "■ EXPERIMENTAL SECTION",
        "EXPERIMENTAL PART",
        "Experimental Part",
        "Experimental part",
        "EXPERIMENTAL BIOLOGICAL PART",
        "EXPERIMENTAL CHEMICAL PART",
        "EXPERIMENTAL",
        "Experimental",
        "Experiment",
    ]
    results_list = [
        "Results and discussions",
        "R E S U L T S  A N D  D I S C U S S I O N",
        "CALCULATIONS, RESULTS, AND DISCUSSION",
        "EXPERIMENT RESULTS AND DISCUSSION",
        "Experiment results and discussion",
        "Experiment Results and Discussion",
        "Experiment Results And Discussion",
        "Experimental Results And Discussion",
        "Experimental results and discussion",
        "Experimental Results and Discussion",
        "Experimental Results and discussion",
        "RESULTS AND DISCUSSION",
        "RESULTS AND DISCUSSIONS",
        "RESULT AND DISCUSSION",
        "Results and Discussion",
        "Result and discussion",
        "Result and Discussion",
        "Results and Discussions",
        "Result and Discussions",
        "Experimental results",
        "Experimental Results",
        "EXPERIMENTAL RESULTS",
        "Results and discussion",
        "Findings",
        "Outcomes",
        "RESULTS",
        "R E S U L T S",
        "Results",
        "Result",
    ]
    materials_methods_list = [
        "Materials and methods",
        "Materials and Method",
        "Material and Methods",
        "Material and Method",
        "Methodology",
        "Methods",
        "Materials",
    ]
    conclusion_list = [
        "Conclusions",
        "CONCLUSION OR SUMMARY AND OUTLOOK",
        "Conclusion or summary and outlook",
        "Conclusion or Summary and Outlook",
        "Conclusions and future prospects",
        "Conclusions and Future Directions",
        "Conclusions and research needs",
        "Conclusion and future research",
        "Conclusions and perspectives",
        "Conclusions and Perspectives",
        "Conclusions and Discussions",
        "Conclusion and Discussion",
        "Conclusions and Discussion",
        "Conclusions and discussion",
        "C O N C L U S I O N S",
        "C O N C L U S I O N",
        "SUMMARY AND OUTLOOK",
        "Summary and Outlook",
        "Summary And Outlook",
        "Summary and outlook",
        "Conclusion",
        "CONCLUSIONS",
        "CONCLUSION",
    ]
    acknowledgments_list = [
        "ACKNOWLEDGMENTS",
        "ACKNOWLEDGEMENTS",
        "A C K N O W L E D G E M E N T S",
        "A C K N O W L E D G M E N T S",
        "Acknowledgments:",
        "Acknowledgments",
        "Acknowledgments",
        "Acknowledgements",
        "Acknowledgement",
        "Acknowlegments",
        "ACKNOWLEDGMENTS",
        "ACKNOWLEDGEMENTS",
        "ACKNOWLEGMENTS",
        "A C K N O W L E D G M E N T S",
        "A C K N O W L E D G E M E N T S",
        "A C K N O W L E D G M E N T S",
        "Conflict  of Interest",
        "conflict of interest"
    ]
    references_list = [
        "REFERENCES",
        "References and notes",
        "References and Notes",
        "Reference List",
        "R E F E R E N C E S",
        "R e f e r e n c e s",
        "References",
        "B I B L I O G R A P H Y",
        "B i b l i o g r a p h y",
        "Bibliography",
        "REFERENCES",
        "References",
        "BIBLIOGRAPHY",
        "B I B L I O G R A P H Y",
        "Bibliography",
    ]
    not_required_list = [
        "Discussions", 
        "DISCUSSIONS", 
        "DISCUSSION",
        "Discussion", 
        "CONFLICT  OF  INTEREST",
    ]
    
    title_list = [
        intro_list,
        abstract_list,
        keywords_list,
        experimental_list,
        results_list,
        materials_methods_list,
        conclusion_list,
        acknowledgments_list,
        references_list,
        not_required_list
    ]
    # pdf_path = r"C:\Users\<USER>\Desktop\final_code_v2\pdf_folder\04760587J.article.002.pdf"
    pdf_path = r"C:\Users\<USER>\Desktop\final_code_v2\pdf_folder\41419292R.article.002.pdf"

    ### test data
    # tan_name = "04760587J"
    tan_name = (pdf_path.split("\\")[-1]).split(".")[0]
    article_size = "LARGE"
    research = True
    
    # Process JSON file
    # json_file_path = r"extracted_data_folder\04760587J.article.002\extracted_data_segmented.json"
    json_file_path = r"extracted_data_folder\41419292R.article.002\extracted_data_segmented.json"

    pdf_content, total_pages, article_size = get_json_text_main(json_file_path, tan_name, title_list, article_size, research)
    
    # Save to JSON file
    custom_segmented_json_path = json_file_path.replace(".json", "_custom.json")
    output_path = save_segmented_json(pdf_content, custom_segmented_json_path)


    get_citationless_segmented_json(pdf_path, custom_segmented_json_path)
    
######################################################################