{"cells": [{"cell_type": "code", "execution_count": 8, "id": "ddb51de2", "metadata": {}, "outputs": [], "source": ["\n", "# Variations for different sections of a document\n", "intro_list = [\n", "    \"INTRODUCTION\",\n", "    \"I N T R O D U C T I O N\",\n", "    \"I n t r o d u c t i o n\",\n", "    \"Introduction\",\n", "    \"Background\",\n", "    \"Introduction and Background\",\n", "    \"B A C K G R O U N D\",\n", "]\n", "abstract_list = [\n", "    \"ABSTRACT\",\n", "    \"Abstract\",\n", "    \"A B S T R A C T\",\n", "    \"a b s t r a c t\",\n", "    \"Summary of Findings\",\n", "    \"Abst.\",\n", "    \"Summary\",\n", "    \"S U M M A R Y\",\n", "]\n", "keywords_list = [\n", "    \"KEYWORDS\",\n", "    \"Keywords\",\n", "    \"<PERSON> E Y W O R D S\",\n", "    \"Key Terms\",\n", "    \"Topics\",\n", "]\n", "experimental_list = [\n", "    \"EXPERIMENTAL PROCEDURES\",\n", "    \"Experimental section\",\n", "    \"Experimental Sections\",\n", "    \"Experimental Section\",\n", "    \"EXPERIMENTAL SECTION\",\n", "    \"Experimental Section\",\n", "    \"Experimental section\",\n", "    \"■ EXPERIMENTAL SECTION\",\n", "    \"EXPERIMENTAL PART\",\n", "    \"Experimental Part\",\n", "    \"Experimental part\",\n", "    \"EXPERIMENTAL BIOLOGICAL PART\",\n", "    \"EXPERIMENTAL CHEMICAL PART\",\n", "    \"EXPERIMENTAL\",\n", "    \"Experimental\",\n", "    \"Experiment\",\n", "]\n", "results_list = [\n", "    \"Results and discussions\",\n", "    \"R E S U L T S  A N D  D I S C U S S I O N\",\n", "    \"CALCULATIONS, RESULTS, AND DISCUSSION\",\n", "    \"EXPERIMENT RESULTS AND DISCUSSION\",\n", "    \"Experiment results and discussion\",\n", "    \"Experiment Results and Discussion\",\n", "    \"Experiment Results And Discussion\",\n", "    \"Experimental Results And Discussion\",\n", "    \"Experimental results and discussion\",\n", "    \"Experimental Results and Discussion\",\n", "    \"Experimental Results and discussion\",\n", "    \"RESULTS AND DISCUSSION\",\n", "    \"RESULTS AND DISCUSSIONS\",\n", "    \"RESULT AND DISCUSSION\",\n", "    \"Results and Discussion\",\n", "    \"Result and discussion\",\n", "    \"Result and Discussion\",\n", "    \"Results and Discussions\",\n", "    \"Result and Discussions\",\n", "    \"Experimental results\",\n", "    \"Experimental Results\",\n", "    \"EXPERIMENTAL RESULTS\",\n", "    \"Results and discussion\",\n", "    \"Findings\",\n", "    \"Outcomes\",\n", "    \"RESULTS\",\n", "    \"R E S U L T S\",\n", "    \"Results\",\n", "    \"Result\",\n", "]\n", "materials_methods_list = [\n", "    \"Materials and methods\",\n", "    \"Materials and Method\",\n", "    \"Material and Methods\",\n", "    \"Material and Method\",\n", "    \"Methodology\",\n", "    \"Methods\",\n", "    \"Materials\",\n", "]\n", "conclusion_list = [\n", "    \"Conclusions\",\n", "    \"CONCLUSION OR SUMMARY AND OUTLOOK\",\n", "    \"Conclusion or summary and outlook\",\n", "    \"Conclusion or Summary and Outlook\",\n", "    \"Conclusions and future prospects\",\n", "    \"Conclusions and Future Directions\",\n", "    \"Conclusions and research needs\",\n", "    \"Conclusion and future research\",\n", "    \"Conclusions and perspectives\",\n", "    \"Conclusions and Perspectives\",\n", "    \"Conclusions and Discussions\",\n", "    \"Conclusion and Discussion\",\n", "    \"Conclusions and Discussion\",\n", "    \"Conclusions and discussion\",\n", "    \"C O N C L U S I O N S\",\n", "    \"C O N C L U S I O N\",\n", "    \"SUMMARY AND OUTLOOK\",\n", "    \"Summary and Outlook\",\n", "    \"Summary And Outlook\",\n", "    \"Summary and outlook\",\n", "    \"Conclusion\",\n", "    \"CONCLUSIONS\",\n", "    \"CONCLUSION\",\n", "]\n", "acknowledgments_list = [\n", "    \"ACKNOWLEDGMENTS\",\n", "    \"ACKNOWLEDGEMENTS\",\n", "    \"A C K N O W L E D G E M E N T S\",\n", "    \"A C K N O W L E D G M E N T S\",\n", "    \"Acknowledgments:\",\n", "    \"Acknowledgments\",\n", "    \"Acknowledgments\",\n", "    \"Acknowledgements\",\n", "    \"Acknowledgement\",\n", "    \"Acknowlegments\",\n", "    \"ACKNOWLEDGMENTS\",\n", "    \"ACKNOWLEDGEMENTS\",\n", "    \"ACKNOWLEGMENTS\",\n", "    \"A C K N O W L E D G M E N T S\",\n", "    \"A C K N O W L E D G E M E N T S\",\n", "    \"A C K N O W L E D G M E N T S\",\n", "    \"Conflict  of Interest\",\n", "    \"conflict of interest\"\n", "]\n", "references_list = [\n", "    \"REFERENCES\",\n", "    \"References and notes\",\n", "    \"References and Notes\",\n", "    \"Reference List\",\n", "    \"R E F E R E N C E S\",\n", "    \"R e f e r e n c e s\",\n", "    \"References\",\n", "    \"B I B L I O G R A P H Y\",\n", "    \"B i b l i o g r a p h y\",\n", "    \"Bibliography\",\n", "    \"REFERENCES\",\n", "    \"References\",\n", "    \"BIBLIOGRAPHY\",\n", "    \"B I B L I O G R A P H Y\",\n", "    \"Bibliography\",\n", "]\n", "not_required_list = [\n", "    \"Discussions\", \n", "    \"DISCUSSIONS\", \n", "    \"DISCUSSION\",\n", "    \"Discussion\", \n", "    \"CONFLICT  OF  INTEREST\",\n", "]\n", "\n", "title_list = [\n", "    intro_list,\n", "    abstract_list,\n", "    keywords_list,\n", "    experimental_list,\n", "    results_list,\n", "    materials_methods_list,\n", "    conclusion_list,\n", "    acknowledgments_list,\n", "    references_list,\n", "    not_required_list\n", "]\n", "\n", "### test data\n", "tan_name = \"04760587J\"\n", "article_size = \"LARGE\"\n", "research = True\n", "\n", "# Process JSON file\n", "json_file_path = r\"extracted_data_folder\\04760587J.article.002\\extracted_data_segmented_sample.json\"\n"]}, {"cell_type": "code", "execution_count": 9, "id": "38b274e7", "metadata": {}, "outputs": [], "source": ["import re\n", "def create_title_regex(title):\n", "    \"\"\"\n", "    Creates a regex pattern to match titles with various flexible formats.\n", "\n", "    Args:\n", "    - title (str): The title to create a regex for.\n", "\n", "    Returns:\n", "    - str: A regex pattern string.\n", "    \"\"\"\n", "    # Convert title to regex that matches flexible formats\n", "    title = re.escape(title)  # Escape special characters\n", "    pattern = rf\"\"\"\n", "        (?<!\\w)                   # Ensure the match is not part of another word\n", "        [A-Z0-9]*\\s*[)|.]*\\s*     # Allow prefixes like \"1\", \"1A)\", \"IV)\", \"■\", \"2 |\"\n", "        {title}                   # Match the actual title text\n", "        (\\s|[A-Z])*               # Allow optional spacing or capitalization within the title\n", "        [^\\w\\n]*                  # Allow any special characters after the title and before newline character\n", "        (?:(?=\\n)|(?=\\s{2,}))     # Ensure the title is followed by a newline or multiple spaces\n", "        (?!\\w)                    # Ensure the match ends cleanly\n", "    \"\"\"\n", "    return pattern"]}, {"cell_type": "code", "execution_count": null, "id": "583ac845", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["header ='Prognostic  value  of  lncRNA  LINC01018  in  prostate cancer  by  regulating  miR-182-5p  (The  role  of LINC01018  in  prostate  cancer)'\n", "Header: Prognostic  value  of  lncRNA  LINC01018  in  prostate cancer  by  regulating  miR-182-5p  (The  role  of LINC01018  in  prostate  cancer) => matched_section: None\n", "Header: Prognostic  value  of  lncRNA  LINC01018  in  prostate cancer  by  regulating  miR-182-5p  (The  role  of LINC01018  in  prostate  cancer) => matched_section: None\n", "Header: Prognostic  value  of  lncRNA  LINC01018  in  prostate cancer  by  regulating  miR-182-5p  (The  role  of LINC01018  in  prostate  cancer) => matched_section: None\n", "Header: Prognostic  value  of  lncRNA  LINC01018  in  prostate cancer  by  regulating  miR-182-5p  (The  role  of LINC01018  in  prostate  cancer) => matched_section: None\n", "Header: Prognostic  value  of  lncRNA  LINC01018  in  prostate cancer  by  regulating  miR-182-5p  (The  role  of LINC01018  in  prostate  cancer) => matched_section: None\n", "Header: Prognostic  value  of  lncRNA  LINC01018  in  prostate cancer  by  regulating  miR-182-5p  (The  role  of LINC01018  in  prostate  cancer) => matched_section: None\n", "Header: Prognostic  value  of  lncRNA  LINC01018  in  prostate cancer  by  regulating  miR-182-5p  (The  role  of LINC01018  in  prostate  cancer) => matched_section: None\n", "Header: Prognostic  value  of  lncRNA  LINC01018  in  prostate cancer  by  regulating  miR-182-5p  (The  role  of LINC01018  in  prostate  cancer) => matched_section: None\n", "Header: Prognostic  value  of  lncRNA  LINC01018  in  prostate cancer  by  regulating  miR-182-5p  (The  role  of LINC01018  in  prostate  cancer) => matched_section: None\n", "Header: Prognostic  value  of  lncRNA  LINC01018  in  prostate cancer  by  regulating  miR-182-5p  (The  role  of LINC01018  in  prostate  cancer) => matched_section: None\n", "header ='ABSTRACT'\n", "Header: ABSTRACT => matched_section: None\n", "Header: ABSTRACT => matched_section: None\n", "Header: ABSTRACT => matched_section: None\n", "Header: ABSTRACT => matched_section: None\n", "Header: ABSTRACT => matched_section: None\n", "Header: ABSTRACT => matched_section: None\n", "Header: ABSTRACT => matched_section: None\n", "Header: ABSTRACT => matched_section: None\n", "Header: ABSTRACT => matched_section: None\n", "Header: ABSTRACT => matched_section: None\n", "header ='ARTICLE HISTORY'\n", "Header: ARTICLE HISTORY => matched_section: None\n", "Header: ARTICLE HISTORY => matched_section: None\n", "Header: ARTICLE HISTORY => matched_section: None\n", "Header: ARTICLE HISTORY => matched_section: None\n", "Header: ARTICLE HISTORY => matched_section: None\n", "Header: ARTICLE HISTORY => matched_section: None\n", "Header: ARTICLE HISTORY => matched_section: None\n", "Header: ARTICLE HISTORY => matched_section: None\n", "Header: ARTICLE HISTORY => matched_section: None\n", "Header: ARTICLE HISTORY => matched_section: None\n", "header ='KEYWORDS'\n", "Header: KEYWORDS => matched_section: None\n", "Header: KEYWORDS => matched_section: None\n", "Header: KEYWORDS => matched_section: None\n", "Header: KEYWORDS => matched_section: None\n", "Header: KEYWORDS => matched_section: None\n", "Header: KEYWORDS => matched_section: None\n", "Header: KEYWORDS => matched_section: None\n", "Header: KEYWORDS => matched_section: None\n", "Header: KEYWORDS => matched_section: None\n", "Header: KEYWORDS => matched_section: None\n", "header ='1.  Introduction'\n", "Header: 1.  Introduction => matched_section: None\n", "Header: 1.  Introduction => matched_section: None\n", "Header: 1.  Introduction => matched_section: None\n", "Header: 1.  Introduction => matched_section: None\n", "Header: 1.  Introduction => matched_section: None\n", "Header: 1.  Introduction => matched_section: None\n", "Header: 1.  Introduction => matched_section: None\n", "Header: 1.  Introduction => matched_section: None\n", "Header: 1.  Introduction => matched_section: None\n", "Header: 1.  Introduction => matched_section: None\n", "header ='3.  Results'\n", "Header: 3.  Results => matched_section: None\n", "Header: 3.  Results => matched_section: None\n", "Header: 3.  Results => matched_section: None\n", "Header: 3.  Results => matched_section: None\n", "Header: 3.  Results => matched_section: None\n", "Header: 3.  Results => matched_section: None\n", "Header: 3.  Results => matched_section: None\n", "Header: 3.  Results => matched_section: None\n", "Header: 3.  Results => matched_section: None\n", "Header: 3.  Results => matched_section: None\n", "header ='3.1.  Relative  expression  levels  of  LINC01018'\n", "Header: 3.1.  Relative  expression  levels  of  LINC01018 => matched_section: None\n", "Header: 3.1.  Relative  expression  levels  of  LINC01018 => matched_section: None\n", "Header: 3.1.  Relative  expression  levels  of  LINC01018 => matched_section: None\n", "Header: 3.1.  Relative  expression  levels  of  LINC01018 => matched_section: None\n", "Header: 3.1.  Relative  expression  levels  of  LINC01018 => matched_section: None\n", "Header: 3.1.  Relative  expression  levels  of  LINC01018 => matched_section: None\n", "Header: 3.1.  Relative  expression  levels  of  LINC01018 => matched_section: None\n", "Header: 3.1.  Relative  expression  levels  of  LINC01018 => matched_section: None\n", "Header: 3.1.  Relative  expression  levels  of  LINC01018 => matched_section: None\n", "Header: 3.1.  Relative  expression  levels  of  LINC01018 => matched_section: None\n", "header ='3.2.  Correlation  between  LINC01018  expression  and  clinical  characteristics  of patients'\n", "Header: 3.2.  Correlation  between  LINC01018  expression  and  clinical  characteristics  of patients => matched_section: None\n", "Header: 3.2.  Correlation  between  LINC01018  expression  and  clinical  characteristics  of patients => matched_section: None\n", "Header: 3.2.  Correlation  between  LINC01018  expression  and  clinical  characteristics  of patients => matched_section: None\n", "Header: 3.2.  Correlation  between  LINC01018  expression  and  clinical  characteristics  of patients => matched_section: None\n", "Header: 3.2.  Correlation  between  LINC01018  expression  and  clinical  characteristics  of patients => matched_section: None\n", "Header: 3.2.  Correlation  between  LINC01018  expression  and  clinical  characteristics  of patients => matched_section: None\n", "Header: 3.2.  Correlation  between  LINC01018  expression  and  clinical  characteristics  of patients => matched_section: None\n", "Header: 3.2.  Correlation  between  LINC01018  expression  and  clinical  characteristics  of patients => matched_section: None\n", "Header: 3.2.  Correlation  between  LINC01018  expression  and  clinical  characteristics  of patients => matched_section: None\n", "Header: 3.2.  Correlation  between  LINC01018  expression  and  clinical  characteristics  of patients => matched_section: None\n", "header ='4.  Discussion'\n", "Header: 4.  Discussion => matched_section: None\n", "Header: 4.  Discussion => matched_section: None\n", "Header: 4.  Discussion => matched_section: None\n", "Header: 4.  Discussion => matched_section: None\n", "Header: 4.  Discussion => matched_section: None\n", "Header: 4.  Discussion => matched_section: None\n", "Header: 4.  Discussion => matched_section: None\n", "Header: 4.  Discussion => matched_section: None\n", "Header: 4.  Discussion => matched_section: None\n", "Header: 4.  Discussion => matched_section: None\n"]}], "source": ["import json\n", "import logging\n", "from coordinate_conversion import convert_bbox_to_fitz, convert_fitz_to_bbox\n", "\n", "\n", "def get_json_text_main(json_file_path, tan_name, title_list, article_size, research):\n", "    \"\"\"\n", "    Process a JSON file to extract structured content from different sections,\n", "    reusing logic from main_pdf_content.py adapted for JSON input.\n", "    \"\"\"\n", "    json_content = {}\n", "    total_pages = 0\n", "\n", "    try:\n", "        with open(json_file_path, 'r', encoding='utf-8') as f:\n", "            data = json.load(f)\n", "\n", "        # Extract page heights for coordinate conversion\n", "        page_heights = {}\n", "        for page_num, page_info in data.get(\"pages\", {}).items():\n", "            if isinstance(page_info, dict) and \"size\" in page_info:\n", "                page_heights[int(page_num)] = page_info[\"size\"].get(\"height\", 0)\n", "        total_pages = len(page_heights)\n", "\n", "        # Initialize containers for sections and page numbers\n", "        header_sections = {title_group[0]: {\"section_text\": []} for title_group in title_list}\n", "        header_location = {title_group[0]: [] for title_group in title_list}\n", "        header_sections[\"PDF_INFO\"] = {\"section_text\": []}\n", "        header_location[\"PDF_INFO\"] = []\n", "\n", "        # Iterate over text_data sections\n", "        for section in data.get(\"text_data\", []):\n", "            header = section.get(\"header\", \"\").strip()\n", "            print(f\"{header =}\")\n", "            section_texts = section.get(\"section_text\", [])\n", "\n", "            matched_section = None\n", "            for title_group in title_list:\n", "                # print(f\"{title_group =}\")\n", "                for title in title_group:\n", "                    # print(f\"{title =}\")\n", "                    pattern = create_title_regex(title)\n", "                    # print(f\"{pattern =}\")\n", "                    if re.search(pattern, header, re.IGNORECASE | re.VERBOSE):  #\n", "                        matched_section = title_group[0]\n", "                        print(f\"{matched_section =}\")\n", "                        break\n", "                if matched_section:\n", "                    break\n", "                print(f\"Header: {header} => matched_section: {matched_section}\")\n", "            if not matched_section:\n", "                matched_section = \"PDF_INFO\"\n", "            \n", "            # Process each text item, convert bbox coordinates\n", "            for text_item in section_texts:\n", "                modified_text_item = {}\n", "                for key, value in text_item.items():\n", "                    if isinstance(value, dict):\n", "                        modified_value = {\n", "                            \"text\": value.get(\"text\", \"\"),\n", "                            \"page_no\": value.get(\"page_no\", 0),\n", "                        }\n", "                        page_no = value.get(\"page_no\", 0)\n", "                        bbox = value.get(\"bbox\", {})\n", "                        page_height = page_heights.get(page_no, 0)\n", "                        if page_height > 0 and all(k in bbox for k in (\"l\", \"t\", \"r\", \"b\")):\n", "                            fitz_coords = convert_bbox_to_fitz(bbox, page_height)\n", "                            modified_value[\"fitz_coords\"] = {\n", "                                \"x0\": fitz_coords[0],\n", "                                \"y0\": fitz_coords[1],\n", "                                \"x1\": fitz_coords[2],\n", "                                \"y1\": fitz_coords[3],\n", "                                \"coord_origin\": \"TOPLEFT\"\n", "                            }\n", "                        modified_text_item[key] = modified_value\n", "                header_sections[matched_section][\"section_text\"].append(modified_text_item)\n", "\n", "                # Track page numbers\n", "                for key, value in text_item.items():\n", "                    if isinstance(value, dict) and \"page_no\" in value:\n", "                        page_no = value[\"page_no\"]\n", "                        if page_no not in header_location[matched_section]:\n", "                            header_location[matched_section].append(page_no)\n", "\n", "        # Build final output dictionary with normalized keys\n", "        output_key_map = {\n", "            \"INTRODUCTION\": \"introduction\",\n", "            \"ABSTRACT\": \"abstract\",\n", "            \"EXPERIMENTAL PROCEDURES\": \"experimental_procedures\",\n", "            \"Results and discussions\": \"results\",\n", "            \"Conclusions\": \"conclusion\",\n", "            \"Materials and methods\": \"materials_methods\",\n", "            \"Discussions\": \"not_required_content\",\n", "            \"PDF_INFO\": \"pdf_info\"\n", "        }\n", "\n", "        for section_name, section_data in header_sections.items():\n", "            if section_data[\"section_text\"]:\n", "                output_key = output_key_map.get(section_name, section_name.lower())\n", "                json_content[output_key] = {\n", "                    \"section_text\": section_data[\"section_text\"],\n", "                    \"page_numbers\": sorted(header_location.get(section_name, []))\n", "                }\n", "\n", "        return json_content, total_pages, article_size\n", "\n", "    except Exception as error:\n", "        logging.error(\"get_json_text_main Error: %s\", str(error))\n", "        return json_content, total_pages, article_size\n", "\n", "\n", "get_json_text_main(json_file_path, tan_name, title_list, article_size, research)"]}, {"cell_type": "code", "execution_count": null, "id": "3375c382", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "9ef1db9c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["title_group =[['EXPERIMENTAL PROCEDURES', 'Experimental section', 'Experimental Sections', 'Experimental Section', 'EXPERIMENTAL SECTION', 'Experimental Section', 'Experimental section', '■ EXPERIMENTAL SECTION', 'EXPERIMENTAL PART', 'Experimental Part', 'Experimental part', 'EXPERIMENTAL BIOLOGICAL PART', 'EXPERIMENTAL CHEMICAL PART', 'EXPERIMENTAL', 'Experimental', 'Experiment'], ['Results and discussions', 'RESULTS', 'R E S U L T S', 'Results', 'Result']]\n", "title_group =[['EXPERIMENTAL PROCEDURES', 'Experimental section', 'Experimental Sections', 'Experimental Section', 'EXPERIMENTAL SECTION', 'Experimental Section', 'Experimental section', '■ EXPERIMENTAL SECTION', 'EXPERIMENTAL PART', 'Experimental Part', 'Experimental part', 'EXPERIMENTAL BIOLOGICAL PART', 'EXPERIMENTAL CHEMICAL PART', 'EXPERIMENTAL', 'Experimental', 'Experiment'], ['Results and discussions', 'RESULTS', 'R E S U L T S', 'Results', 'Result']]\n", "title ='Results'\n", "pattern ='\\n        (?<!\\\\w)                   # Ensure the match is not part of another word\\n        [A-Z0-9]*\\\\s*[)|.]*\\\\s*     # Allow prefixes like \"1\", \"1A)\", \"IV)\", \"■\", \"2 |\"\\n        Results                   # Match the actual title text\\n        (\\\\s|[A-Z])*               # Allow optional spacing or capitalization within the title\\n        [^\\\\w\\\\n]*                  # Allow any special characters after the title and before newline character\\n        (?:(?=\\\\n)|(?=\\\\s(2,)))     # Ensure the title is followed by a newline or multiple spaces\\n        (?!\\\\w)                    # Ensure the match ends cleanly\\n    '\n"]}], "source": ["import re\n", "def create_title_regex(title):\n", "    \"\"\"\n", "    Creates a regex pattern to match titles with various flexible formats.\n", "\n", "    Args:\n", "    - title (str): The title to create a regex for.\n", "\n", "    Returns:\n", "    - str: A regex pattern string.\n", "    \"\"\"\n", "    # Convert title to regex that matches flexible formats\n", "    title = re.escape(title)  # Escape special characters\n", "    pattern = rf\"\"\"\n", "        (?<!\\w)                   # Ensure the match is not part of another word\n", "        [A-Z0-9]*\\s*[)|.]*\\s*     # Allow prefixes like \"1\", \"1A)\", \"IV)\", \"■\", \"2 |\"\n", "        {title}                   # Match the actual title text\n", "        (\\s|[A-Z])*               # Allow optional spacing or capitalization within the title\n", "        [^\\w\\n]*                  # Allow any special characters after the title and before newline character\n", "        (?:(?=\\n)|(?=\\s{2,}))     # Ensure the title is followed by a newline or multiple spaces\n", "        (?!\\w)                    # Ensure the match ends cleanly\n", "    \"\"\"\n", "    return pattern\n", "\n", "title_group = [\n", "    experimental_list,\n", "    results_list,\n", "\n", "]\n", "\n", "results_list = [\n", "    \"Results and discussions\",\n", "    \"RESULTS\",\n", "    \"R E S U L T S\",\n", "    \"Results\",\n", "    \"Result\",\n", "]\n", "header =\"3.  Results\"\n", "\n", "matched_section = \"The  LINC01018  expression  in  prostate  cancer  tissues  and  cell  samples  in this  experiment was determined by RT-qPCR,\"\n", "\n", "# print(f\"{title_group =}\")\n", "for title_item in title_group:\n", "    print(f\"{title_group =}\")\n", "    for title in title_item:\n", "        if title == \"Results\":\n", "            print(f\"{title =}\")\n", "            pattern = create_title_regex(title)\n", "            # print(f\"{pattern =}\")\n", "            if re.search(pattern, header, re.IGNORECASE | re.VERBOSE):  #\n", "                matched_section = title_group[0]\n", "                print(f\"{matched_section =}\")"]}, {"cell_type": "code", "execution_count": null, "id": "3038742f", "metadata": {}, "outputs": [], "source": ["import re\n", "import json\n", "from coordinate_conversion import convert_bbox_to_fitz\n", "\n", "def create_title_regex(title):\n", "    title = re.escape(title)\n", "    pattern = rf\"\"\"\n", "        (?<!\\w)                   # Not part of another word\n", "        [A-Z0-9]*\\s*[)|.]*\\s*     # Allow prefixes like 1, 1A), IV), etc.\n", "        {title}                   # Actual title text\n", "        (\\s|[A-Z])*               # Optional spacing or caps\n", "        [^\\w\\n]*                  # Any special chars before newline\n", "        (?:(?=\\n)|(?=\\s{{2,}}))   # Followed by newline or multiple spaces\n", "        (?!\\w)                    # End cleanly\n", "    \"\"\"\n", "    return pattern\n", "\n", "def get_json_text_main(json_file_path, tan_name, title_list, article_size, research):\n", "    json_content = {}\n", "    total_pages = 0\n", "\n", "    # Create mapping from first item in each title group to normalized output key\n", "    title_group_to_key = {\n", "        intro_list[0]: \"introduction\",\n", "        abstract_list[0]: \"abstract\",\n", "        keywords_list[0]: \"keywords\",\n", "        experimental_list[0]: \"experimental_procedures\",\n", "        results_list[0]: \"results\",\n", "        materials_methods_list[0]: \"materials_methods\",\n", "        conclusion_list[0]: \"conclusion\",\n", "        acknowledgments_list[0]: \"acknowledgments\",\n", "        references_list[0]: \"references\",\n", "        not_required_list[0]: \"not_required_content\"\n", "    }\n", "\n", "    try:\n", "        with open(json_file_path, 'r', encoding='utf-8') as f:\n", "            data = json.load(f)\n", "\n", "        page_heights = {}\n", "        for page_num, page_info in data.get(\"pages\", {}).items():\n", "            if isinstance(page_info, dict) and \"size\" in page_info:\n", "                page_heights[int(page_num)] = page_info[\"size\"].get(\"height\", 0)\n", "        total_pages = len(page_heights)\n", "\n", "        # Setup storage\n", "        header_sections = {key: {\"section_text\": []} for key in title_group_to_key.values()}\n", "        header_sections[\"pdf_info\"] = {\"section_text\": []}\n", "\n", "        header_location = {key: [] for key in title_group_to_key.values()}\n", "        header_location[\"pdf_info\"] = []\n", "\n", "        for section in data.get(\"text_data\", []):\n", "            header = section.get(\"header\", \"\").strip()\n", "            section_texts = section.get(\"section_text\", [])\n", "            matched_section_key = \"pdf_info\"\n", "\n", "            for title_group in title_list:\n", "                for title in title_group:\n", "                    pattern = create_title_regex(title)\n", "                    if re.search(pattern, header, re.IGNORECASE | re.VERBOSE):\n", "                        matched_section_key = title_group_to_key.get(title_group[0], \"pdf_info\")\n", "                        break\n", "                if matched_section_key != \"pdf_info\":\n", "                    break\n", "\n", "            for text_item in section_texts:\n", "                modified_text_item = {}\n", "                for key, value in text_item.items():\n", "                    if isinstance(value, dict):\n", "                        modified_value = {\n", "                            \"text\": value.get(\"text\", \"\"),\n", "                            \"page_no\": value.get(\"page_no\", 0)\n", "                        }\n", "                        page_no = value.get(\"page_no\", 0)\n", "                        bbox = value.get(\"bbox\", {})\n", "                        page_height = page_heights.get(page_no, 0)\n", "\n", "                        if page_height and all(k in bbox for k in (\"l\", \"t\", \"r\", \"b\")):\n", "                            fitz_coords = convert_bbox_to_fitz(bbox, page_height)\n", "                            modified_value[\"fitz_coords\"] = {\n", "                                \"x0\": fitz_coords[0],\n", "                                \"y0\": fitz_coords[1],\n", "                                \"x1\": fitz_coords[2],\n", "                                \"y1\": fitz_coords[3],\n", "                                \"coord_origin\": \"TOPLEFT\"\n", "                            }\n", "                        modified_text_item[key] = modified_value\n", "\n", "                header_sections[matched_section_key][\"section_text\"].append(modified_text_item)\n", "\n", "                for key, value in text_item.items():\n", "                    if isinstance(value, dict) and \"page_no\" in value:\n", "                        page_no = value[\"page_no\"]\n", "                        if page_no not in header_location[matched_section_key]:\n", "                            header_location[matched_section_key].append(page_no)\n", "\n", "        # Build final output\n", "        for section_key, section_data in header_sections.items():\n", "            if section_data[\"section_text\"]:\n", "                json_content[section_key] = {\n", "                    \"section_text\": section_data[\"section_text\"],\n", "                    \"page_numbers\": sorted(header_location.get(section_key, []))\n", "                }\n", "\n", "        return json_content, total_pages, article_size\n", "\n", "    except Exception as error:\n", "        print(f\"Error processing JSON: {error}\")\n", "        return {}, 0, article_size\n", "pdf_content, total_pages, article_size = get_json_text_main(json_file_path, tan_name, title_list, article_size, research)"]}, {"cell_type": "code", "execution_count": 34, "id": "9dec2cce", "metadata": {}, "outputs": [], "source": ["import re\n", "import json\n", "from coordinate_conversion import convert_bbox_to_fitz\n", "\n", "# def create_title_regex(title):\n", "#     title = re.escape(title)\n", "#     pattern = rf\"\"\"\n", "#         (?<!\\w)                   # Not part of another word\n", "#         [A-Z0-9]*\\s*[)|.]*\\s*     # Allow prefixes like 1, 1A), IV), etc.\n", "#         {title}                   # Actual title text\n", "#         (\\s|[A-Z])*               # Optional spacing or caps\n", "#         [^\\w\\n]*                  # Any special chars before newline\n", "#         (?:(?=\\n)|(?=\\s{{2,}}))   # Followed by newline or multiple spaces\n", "#         (?!\\w)                    # End cleanly\n", "#     \"\"\"\n", "#     return pattern\n", "def create_title_regex(title):\n", "    title = re.escape(title.strip())\n", "    pattern = rf\"(?i)\\b(?:[0-9A-Z]+\\s*[.)]?\\s*)?{title}\\b\"\n", "    return pattern\n", "\n", "def get_json_text_main(json_file_path, tan_name, title_list, article_size, research):\n", "    json_content = {}\n", "    total_pages = 0\n", "\n", "    # Create mapping from first item in each title group to normalized output key\n", "    title_group_to_key = {\n", "        intro_list[0]: \"introduction\",\n", "        abstract_list[0]: \"abstract\",\n", "        keywords_list[0]: \"keywords\",\n", "        experimental_list[0]: \"experimental_procedures\",\n", "        results_list[0]: \"results\",\n", "        materials_methods_list[0]: \"materials_methods\",\n", "        conclusion_list[0]: \"conclusion\",\n", "        acknowledgments_list[0]: \"acknowledgments\",\n", "        references_list[0]: \"references\",\n", "        not_required_list[0]: \"not_required_content\"\n", "    }\n", "\n", "    try:\n", "        with open(json_file_path, 'r', encoding='utf-8') as f:\n", "            data = json.load(f)\n", "\n", "        # Get page heights if available (for fitz bbox conversion)\n", "        page_heights = {}\n", "        for page_num, page_info in data.get(\"pages\", {}).items():\n", "            if isinstance(page_info, dict) and \"size\" in page_info:\n", "                page_heights[int(page_num)] = page_info[\"size\"].get(\"height\", 0)\n", "        total_pages = len(page_heights)\n", "\n", "        # Setup containers\n", "        header_sections = {key: {\"section_text\": []} for key in title_group_to_key.values()}\n", "        header_sections[\"pdf_info\"] = {\"section_text\": []}\n", "        header_location = {key: [] for key in title_group_to_key.values()}\n", "        header_location[\"pdf_info\"] = []\n", "\n", "        current_matched_key = \"pdf_info\"\n", "\n", "        # Process each section\n", "        for section in data.get(\"text_data\", []):\n", "            header = section.get(\"header\", \"\").strip()\n", "            section_texts = section.get(\"section_text\", [])\n", "            matched_section_key = None\n", "\n", "            # Check if current header matches any title\n", "            for title_group in title_list:\n", "                for title in title_group:\n", "                    pattern = create_title_regex(title)\n", "                    if re.search(pattern, header, re.IGNORECASE | re.VERBOSE):\n", "                        matched_section_key = title_group_to_key.get(title_group[0], \"pdf_info\")\n", "                        break\n", "                if matched_section_key:\n", "                    break\n", "\n", "            # Update current matched key if a new one is found\n", "            if matched_section_key:\n", "                current_matched_key = matched_section_key\n", "\n", "            # Add header as pseudo-section_text (if it's not empty)\n", "            if header:\n", "                pseudo_text = {\n", "                    \"text\": header,\n", "                    \"page_no\": section_texts[0][list(section_texts[0].keys())[0]].get(\"page_no\", 0)\n", "                    if section_texts else 0\n", "                }\n", "                header_sections[current_matched_key][\"section_text\"].append({\"header\": pseudo_text})\n", "                page_no = pseudo_text[\"page_no\"]\n", "                if page_no not in header_location[current_matched_key]:\n", "                    header_location[current_matched_key].append(page_no)\n", "\n", "            # Process actual section text\n", "            for text_item in section_texts:\n", "                for key, value in text_item.items():\n", "                    if isinstance(value, dict):\n", "                        modified_value = {\n", "                            \"text\": value.get(\"text\", \"\"),\n", "                            \"page_no\": value.get(\"page_no\", 0)\n", "                        }\n", "                        page_no = value.get(\"page_no\", 0)\n", "                        bbox = value.get(\"bbox\", {})\n", "                        page_height = page_heights.get(page_no, 0)\n", "\n", "                        if page_height and all(k in bbox for k in (\"l\", \"t\", \"r\", \"b\")):\n", "                            fitz_coords = convert_bbox_to_fitz(bbox, page_height)\n", "                            modified_value[\"fitz_coords\"] = {\n", "                                \"x0\": fitz_coords[0],\n", "                                \"y0\": fitz_coords[1],\n", "                                \"x1\": fitz_coords[2],\n", "                                \"y1\": fitz_coords[3],\n", "                                \"coord_origin\": \"TOPLEFT\"\n", "                            }\n", "\n", "                        header_sections[current_matched_key][\"section_text\"].append({key: modified_value})\n", "                        if page_no not in header_location[current_matched_key]:\n", "                            header_location[current_matched_key].append(page_no)\n", "\n", "        # Compile final output\n", "        for section_key, section_data in header_sections.items():\n", "            if section_data[\"section_text\"]:\n", "                json_content[section_key] = {\n", "                    \"section_text\": section_data[\"section_text\"],\n", "                    \"page_numbers\": sorted(header_location.get(section_key, []))\n", "                }\n", "\n", "        return json_content, total_pages, article_size\n", "\n", "    except Exception as error:\n", "        print(f\"Error processing JSON: {error}\")\n", "        return {}, 0, article_size\n", "\n", "\n", "pdf_content, total_pages, article_size = get_json_text_main(json_file_path, tan_name, title_list, article_size, research)"]}, {"cell_type": "code", "execution_count": 35, "id": "c87c1d73", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'introduction': {'section_text': [{'header': {'text': '1.  Introduction',\n", "     'page_no': 1}},\n", "   {'#/texts/11': {'text': 'Prostate  cancer  is  a  male  tumor  that  often  occurs  in  the  urinary  system, with  high  morbidity  and  mortality. [1-2] The  pathogenic  factors  of  prostate cancer  have  not  been  fully  elucidated,  there  are  studies  showing  that  the incidence  of  prostate  cancer  is  related  to  genetics,  environment,  sexual activity,  and  eating  habits. [3-4] Prostate  cancer  patients  can  be  effectively treated  with  excision  surgery,  radiation  therapy,  and  hormone  therapy  at this  stage.  However,  it  has  been  reported  that  60%  of  prostate  cancer patients  will  experience  biochemical  recurrence  after  radical  prostatectomy. [5] It  is  meaningful  to  monitor  the  prognostic  effect  of  patients  by molecular  means  to  study  more  reliable  prognostic  biomarkers.',\n", "     'page_no': 1,\n", "     'fitz_coords': {'x0': 72.0,\n", "      'y0': 408.016,\n", "      'x1': 434.682,\n", "      'y1': 544.227,\n", "      'coord_origin': 'TOPLEFT'}}},\n", "   {'#/texts/12': {'text': 'The application of molecular biology in diseases has found that lncRNAs are  involved  in  a  variety  of  cellular  metabolism,  cycle  regulation,  transcription  and  translation,  in  which  abnormal  expression  has  been  confirmed to be related to human tumors. [6-7] Moreover, lncRNAs are identified as  potential  marker  that  affects  the  diagnosis,  treatment  and  prognosis  of',\n", "     'page_no': 1,\n", "     'fitz_coords': {'x0': 72.0,\n", "      'y0': 548.016,\n", "      'x1': 434.657,\n", "      'y1': 614.227,\n", "      'coord_origin': 'TOPLEFT'}}},\n", "   {'#/texts/13': {'text': 'CONTACT Min  cao <EMAIL> department  of  urology  Andrology,  sinopharm  dongfeng General  Hospital,  Hubei  university  of  Medicine,  No.  16,  daling  Road,  Zhangwan  district,  shiyan,  Hubei,  442008, china.',\n", "     'page_no': 1,\n", "     'fitz_coords': {'x0': 72.0,\n", "      'y0': 629.858,\n", "      'x1': 433.573,\n", "      'y1': 654.673,\n", "      'coord_origin': 'TOPLEFT'}}},\n", "   {'#/texts/14': {'text': '*these  two  authors  contributed  equally  to  this  text.',\n", "     'page_no': 1,\n", "     'fitz_coords': {'x0': 72.0,\n", "      'y0': 656.858,\n", "      'x1': 238.192,\n", "      'y1': 663.673,\n", "      'coord_origin': 'TOPLEFT'}}},\n", "   {'#/texts/18': {'text': 'the  disease.  For  example,  lncRNA  MEG3  as  down-regulated  RNA,  that affected  the  proliferation  level  and  apoptosis  of  prostate  cancer  cells. [8] LncRNA OIP5-AS1 promoted the development of prostate cancer through regulating  the  miR-128-3p/SLC7A11  axis. [9]   There  were  evidences  that lncRNA H19, as a predictive marker for neuroendocrine prostate cancer, [10] also  contributed  to  the  treatment  of  lung  cancer, [11] and  that  H19  competitively  bound  miR-140  to  promote  ovarian  cancer  cells  proliferation. [12] Likewise, LINC01018 has also been demonstrated as biomarker for different diseases, such as liver cancer, leukemia, and colorectal cancer. [13-15] However, LINC01018  has  not  been  reported  in  prostate  cancer  research,  which sparked  interest  in  further  research.',\n", "     'page_no': 2,\n", "     'fitz_coords': {'x0': 72.0,\n", "      'y0': 53.21900000000005,\n", "      'x1': 434.686,\n", "      'y1': 203.42999999999995,\n", "      'coord_origin': 'TOPLEFT'}}},\n", "   {'#/texts/19': {'text': 'Evidence  has  shown  that  lncRNAs  act  as  endogenous  sponges  to  regulate  various  biological  processes  in  tumors  by  binding  to  miRNAs. [16] According  to  the  close  correlation  between  lncRNAs  and  miRNAs,  as well  as  the  association  between  lncRNAs  and  tumors,  it  is  speculated that  abnormal  expression  of  lncRNAs  will  interfere  with  the  normal expression and efficacy of miRNAs, and then have a positive or negative impact  on  tumor  progression. [17] Therefore,  the  role  and  mechanism  of LINC01018  regulating  miRNA  in  prostate  cancer  is  a  topic  worthy  of discussion.',\n", "     'page_no': 2,\n", "     'fitz_coords': {'x0': 72.0,\n", "      'y0': 207.21900000000005,\n", "      'x1': 434.643,\n", "      'y1': 329.431,\n", "      'coord_origin': 'TOPLEFT'}}},\n", "   {'#/texts/20': {'text': 'The current study explored the expression of LINC01018 and its targeted factor  miR-182-5p  in  prostate  cancer  to  understand  its  mechanism  of action  and  biological  effect,  providing  a  theoretical  basis  for  the  clinical application  of  LINC01018  as  a  new  prognostic  factor  in  prostate  cancer.',\n", "     'page_no': 2,\n", "     'fitz_coords': {'x0': 72.0,\n", "      'y0': 333.219,\n", "      'x1': 434.65,\n", "      'y1': 385.431,\n", "      'coord_origin': 'TOPLEFT'}}}],\n", "  'page_numbers': [1, 2]},\n", " 'abstract': {'section_text': [{'header': {'text': 'ABSTRACT', 'page_no': 1}},\n", "   {'#/texts/5': {'text': 'LncRNAs  are  abnormally  expressed  in  a  variety  of  cancers  and play  unique  roles  in  therapy.  Based  on  this,  the  prognostic value of lncRNA LINC01018 in prostate cancer was discussed in this  study.  LINC01018  was  underexpressed  in  prostate  cancer tissues  and  cells,  while  miR-182-5p  was  elevated  (*** p <  0.001). Overexpression  of  LINC01018  may  inhibit  the  progression  of prostate  cancer  by  targeting  miR-182-5p.  This  study  revealed that upregulated LINC01018 may prolong the overall survival of patients with prostate cancer (log-rank p =  0.042), and LINC01018 may become a prognostic biomarker for patients with prostate cancer, which  brings  a  new  direction  for  the  treatment  of patients.',\n", "     'page_no': 1,\n", "     'fitz_coords': {'x0': 84.0,\n", "      'y0': 234.43599999999998,\n", "      'x1': 325.938,\n", "      'y1': 352.095,\n", "      'coord_origin': 'TOPLEFT'}}},\n", "   {'header': {'text': 'ARTICLE HISTORY', 'page_no': 1}},\n", "   {'#/texts/7': {'text': 'Received  9  November 2022 Accepted  18  December 2023',\n", "     'page_no': 1,\n", "     'fitz_coords': {'x0': 342.5,\n", "      'y0': 233.235,\n", "      'x1': 424.824,\n", "      'y1': 267.043,\n", "      'coord_origin': 'TOPLEFT'}}}],\n", "  'page_numbers': [1]},\n", " 'keywords': {'section_text': [{'header': {'text': 'KEYWORDS', 'page_no': 1}},\n", "   {'#/texts/9': {'text': 'Prostate  cancer;  lncRNA LINC01018; overexpression;  miR- 182-5p;  prognosis',\n", "     'page_no': 1,\n", "     'fitz_coords': {'x0': 342.5,\n", "      'y0': 284.41,\n", "      'x1': 425.755,\n", "      'y1': 318.218,\n", "      'coord_origin': 'TOPLEFT'}}}],\n", "  'page_numbers': [1]},\n", " 'results': {'section_text': [{'header': {'text': '3.  Results',\n", "     'page_no': 0}},\n", "   {'header': {'text': '3.1.  Relative  expression  levels  of  LINC01018',\n", "     'page_no': 5}},\n", "   {'#/texts/51': {'text': 'The  LINC01018  expression  in  prostate  cancer  tissues  and  cell  samples  in this  experiment was determined by RT-qPCR, and normal tissues and cells were  used  as  the  control  group.  As  a  result,  LINC01018  was  decreased  in prostate  cancer  tissues  (Figure  1A)  and  similarly  decreased  in  prostate cancer  cells  (Figure  1B).',\n", "     'page_no': 5,\n", "     'fitz_coords': {'x0': 72.0,\n", "      'y0': 247.73399999999998,\n", "      'x1': 434.643,\n", "      'y1': 313.945,\n", "      'coord_origin': 'TOPLEFT'}}},\n", "   {'header': {'text': '3.2.  Correlation  between  LINC01018  expression  and  clinical  characteristics  of patients',\n", "     'page_no': 5}},\n", "   {'#/texts/53': {'text': 'All  prostate  cancer  patients  participating  in  the  experiment  were  divided into  low  expression  of  LINC01018  and  high  expression  of  LINC01018, which  were  grouped  according  to  the  average  expression  of  LINC01018 in  prostate  cancer  tissue.  Table  1  presented  the  relationship  between LINC01018 expression and clinical parameters of prostate cancer patients.',\n", "     'page_no': 5,\n", "     'fitz_coords': {'x0': 72.0,\n", "      'y0': 373.734,\n", "      'x1': 434.7,\n", "      'y1': 439.945,\n", "      'coord_origin': 'TOPLEFT'}}},\n", "   {'#/texts/56': {'text': \"Among  many  characteristic  parameters,  LINC01018  expression  was  significantly correlated with lymph node metastasis and TNM stage ( p =  0.007, p =  0.002),  while  there  was  no  correlation  with  patients'  age,  gleason  score, or  differentiation.\",\n", "     'page_no': 6,\n", "     'fitz_coords': {'x0': 72.0,\n", "      'y0': 53.21900000000005,\n", "      'x1': 434.677,\n", "      'y1': 105.42999999999995,\n", "      'coord_origin': 'TOPLEFT'}}}],\n", "  'page_numbers': [0, 5, 6]},\n", " 'not_required_content': {'section_text': [{'header': {'text': '4.  Discussion',\n", "     'page_no': 9}},\n", "   {'#/texts/125': {'text': 'The  global  cancer  statistics  report  shows  that  the  incidence  of  prostate cancer ranks second among male cancers. [18]  However, conventional clinical  treatment  methods  will  produce  certain  side  effects  and  sequelae, affecting  the  normal  life  of  prostate  cancer  patients.  Therefore,  there  is an  urgent  need  to  explore  new  therapeutic  and  prognostic  approaches. In  existing  prostate  cancer-related  studies,  the  expression  levels  of  many lncRNAs  are  associated  with  patient  prognosis.  <PERSON>  et  al.  revealed  a novel  molecular  mechanism  mediated  by  lncRNA  PCAT6,  a  prognostic factor  for  the  treatment  of  bone-metastatic  prostate  cancer. [19] <PERSON><PERSON>  and several  others  noticed  that  PCA3,  PCAT18,  HOTAIR,  and  CCAT2  were up-regulated in prostate cancer, which was expected to be specific markers  for  application. [20] Additionally,  <PERSON><PERSON>  and  colleagues  also  proposed that  RNA methyltransferase METTL1 mediated methylation to influence prostate  cancer  treatment  outcomes. [21] LINC01018  is  a  novel  lncRNA located  on  human  chromosome  5  (5p15.31). [22]   According  to  existing evidence,  LINC01018  can  not  only  affect  the  deterioration  of  glioma  by mediating  miR-942-5p/KNG1  axis, [23]   but  also  regulate  PDCD4 via targeting miR-499a-5p to slow the progression of acute myeloid leukemia. [14] In  addition,  the  LINC01018/miR-197-3p/GNA14  network  proposed  by <PERSON><PERSON>  et  al.  explained  the  prognostic  mechanism  in  hepatocellular  carcinoma. [24] This  study  found  that  LINC01018  expression  was  decreased  in prostate  tissues  and  cells,  and  low  expression  of  LINC01018  was  proven to  be  detrimental  to  patient  survival.  Therefore,  LINC01018  is  likely  to be an independent prognostic and curative biomarker for prostate cancer.',\n", "     'page_no': 9,\n", "     'fitz_coords': {'x0': 72.0,\n", "      'y0': 74.28200000000004,\n", "      'x1': 434.611,\n", "      'y1': 410.482,\n", "      'coord_origin': 'TOPLEFT'}}}],\n", "  'page_numbers': [9]},\n", " 'pdf_info': {'section_text': [{'header': {'text': 'Prognostic  value  of  lncRNA  LINC01018  in  prostate cancer  by  regulating  miR-182-5p  (The  role  of LINC01018  in  prostate  cancer)',\n", "     'page_no': 1}},\n", "   {'#/texts/3': {'text': '<PERSON><PERSON><PERSON>*, <PERSON><PERSON><PERSON>*,  <PERSON><PERSON>,  <PERSON><PERSON><PERSON>  and  Min  Cao department  of  urology  Andrology,  sinopharm  dongfeng  General  Hospital,  Hubei  university  of Medicine,  shiyan,  Hubei,  china',\n", "     'page_no': 1,\n", "     'fitz_coords': {'x0': 72.0,\n", "      'y0': 164.42499999999995,\n", "      'x1': 384.298,\n", "      'y1': 173.361,\n", "      'coord_origin': 'TOPLEFT'}}}],\n", "  'page_numbers': [1]}}"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["pdf_content"]}, {"cell_type": "code", "execution_count": null, "id": "57abc473", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "88f270c4", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "envnlp", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.8"}}, "nbformat": 4, "nbformat_minor": 5}