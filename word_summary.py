import os
import re
import pandas as pd
from docx import Document

def extract_feedback_from_text(text):
    entries = []
    current_tan = None
    lines = [line.strip() for line in text.splitlines() if line.strip()]

    for line in lines:
        # TAN identification
        match_tan = re.match(r"-Feedback for (\w+)", line)
        if match_tan:
            current_tan = match_tan.group(1)
            entry = {
                'tan_number': current_tan,
                'feed_back': line,
                'feed_back_catagory': 'feedback',
            }
            entry['feed_back_type'] = classify_feedback_type(entry['feed_back'], entry['feed_back_catagory'])
            entries.append(entry)
        else:
            if not current_tan:
                continue  # Skip until a TAN is found
            category = classify_feedback_category(line)
            entry = {
                'tan_number': current_tan,
                'feed_back': line,
                'feed_back_catagory': category,
            }
            entry['feed_back_type'] = classify_feedback_type(entry['feed_back'], entry['feed_back_catagory'])
            entries.append(entry)
    return entries

# def classify_feedback_category(line):
#     line_lower = line.lower()

#     if "crf" in line_lower:
#         return "CRF addition"
#     elif "cth" in line_lower:
#         return "CTH addition" if "added" in line_lower else "CTH deletion"
#     elif "par" in line_lower:
#         return "PAR addition" if "added" in line_lower else "PAR deletion"
#     elif "hmd" in line_lower:
#         return "HMD addition" if "added" in line_lower else "HMD deletion"
#     elif any(term in line_lower for term in ["modified ttl", "modified abs", "modified key"]):
#         return "modification"
#     else:
#         return "other"


# def classify_feedback_category(line):
#     line_lower = line.lower()

#     # Specific keyword-based classification
#     if "crf" in line_lower and "added" in line_lower:
#         return "CRF addition"
#     elif "crf" in line_lower and any(word in line_lower for word in ["deleted", "removed"]):
#         return "CRF deletion"

#     elif "cth" in line_lower and "added" in line_lower:
#         return "CTH addition"
#     elif "cth" in line_lower and any(word in line_lower for word in ["deleted", "removed", "killed"]):
#         return "CTH deletion"
#     elif "cth" in line_lower and "modified" in line_lower:
#         return "CTH modification"

#     elif "par" in line_lower and "added" in line_lower:
#         return "PAR addition"
#     elif "par" in line_lower and any(word in line_lower for word in ["deleted", "removed"]):
#         return "PAR deletion"

#     elif "hmd" in line_lower and "added" in line_lower:
#         return "HMD addition"
#     elif "hmd" in line_lower and any(word in line_lower for word in ["deleted", "removed"]):
#         return "HMD deletion"

#     elif "ttl" in line_lower and "modified" in line_lower:
#         return "Title modification"
#     elif "abs" in line_lower and "modified" in line_lower:
#         return "Abstract modification"
#     elif "key" in line_lower and "modified" in line_lower:
#         return "Keyword modification"

#     elif "subsection" in line_lower and "added" in line_lower:
#         return "Subsection addition"
#     elif "subsection" in line_lower and "modified" in line_lower:
#         return "Subsection modification"
#     elif "subsection" in line_lower and any(word in line_lower for word in ["deleted", "removed"]):
#         return "Subsection deletion"

#     # else category
#     return "other"


def classify_feedback_category(line):
    line_lower = line.lower()

    def keyword_order_check(before_kw, after_kw):
        """Check if `before_kw` appears before `after_kw` in the line"""
        return line_lower.find(before_kw) < line_lower.find(after_kw)

    # --- Priority: Order-sensitive checks ---
    if "added" in line_lower:
        if "hmd" in line_lower and keyword_order_check("added", "hmd"):
            return "HMD addition"
        if "cth" in line_lower and keyword_order_check("added", "cth"):
            return "CTH addition"
        if "par" in line_lower and keyword_order_check("added", "par"):
            return "PAR addition"
        if "crf" in line_lower and keyword_order_check("added", "crf"):
            return "CRF addition"
        if "subsection" in line_lower and keyword_order_check("added", "subsection"):
            return "Subsection addition"

    if "modified" in line_lower:
        if "cth" in line_lower and keyword_order_check("modified", "cth"):
            return "CTH modification"
        if "ttl" in line_lower:
            return "Title modification"
        if "abs" in line_lower:
            return "Abstract modification"
        if "key" in line_lower:
            return "Keyword modification"
        if "subsection" in line_lower:
            return "Subsection modification"

    if any(word in line_lower for word in ["deleted", "removed", "killed"]):
        if "hmd" in line_lower:
            return "HMD deletion"
        if "cth" in line_lower:
            return "CTH deletion"
        if "par" in line_lower:
            return "PAR deletion"
        if "crf" in line_lower:
            return "CRF deletion"
        if "subsection" in line_lower:
            return "Subsection deletion"

    # --- Fallback to previous logic if order not detected ---
    if "crf" in line_lower and "added" in line_lower:
        return "CRF addition"
    elif "crf" in line_lower and any(word in line_lower for word in ["deleted", "removed"]):
        return "CRF deletion"

    elif "cth" in line_lower and "added" in line_lower:
        return "CTH addition"
    elif "cth" in line_lower and any(word in line_lower for word in ["deleted", "removed", "killed"]):
        return "CTH deletion"
    elif "cth" in line_lower and "modified" in line_lower:
        return "CTH modification"

    elif "par" in line_lower and "added" in line_lower:
        return "PAR addition"
    elif "par" in line_lower and any(word in line_lower for word in ["deleted", "removed"]):
        return "PAR deletion"

    elif "hmd" in line_lower and "added" in line_lower:
        return "HMD addition"
    elif "hmd" in line_lower and any(word in line_lower for word in ["deleted", "removed"]):
        return "HMD deletion"

    elif "ttl" in line_lower and "modified" in line_lower:
        return "Title modification"
    elif "abs" in line_lower and "modified" in line_lower:
        return "Abstract modification"
    elif "key" in line_lower and "modified" in line_lower:
        return "Keyword modification"

    elif "subsection" in line_lower and "added" in line_lower:
        return "Subsection addition"
    elif "subsection" in line_lower and "modified" in line_lower:
        return "Subsection modification"
    elif "subsection" in line_lower and any(word in line_lower for word in ["deleted", "removed"]):
        return "Subsection deletion" 

    return "other" 




def classify_feedback_type(feedback, category):
    feedback_lower = feedback.lower()
    category_lower = category.lower()

    # Positive feedback
    if any(phrase in feedback_lower for phrase in ["good job", "well done", "very good"]):
        return "pos"

    # Negative feedback
    if any(cat in category_lower for cat in ["deletion", "cth deletion", "par deletion", "num deletion"]) and \
       any(word in feedback_lower for word in ["killed", "deleted"]):
        return "neg"

    # Compound feedback
    if any(cat in category_lower for cat in ["cth addition", "cth modification", "sec-sub modification"]):
        if feedback_lower.startswith("added cth") or feedback_lower.startswith("changed cth") or "subsection" in feedback_lower:
            return "compound"

    # else neutral
    return "neu"


def read_docx_files(folder_path):
    all_entries = []
    for filename in os.listdir(folder_path):
        if filename.endswith(".docx"):
            full_path = os.path.join(folder_path, filename)
            doc = Document(full_path)
            full_text = "\n".join([para.text for para in doc.paragraphs])
            entries = extract_feedback_from_text(full_text)
            all_entries.extend(entries)
    return pd.DataFrame(all_entries)

def save_to_excel(df, output_file):
    df.to_excel(output_file, index=False, engine='openpyxl')
    print(f"Saved to {output_file}")



# === Main Execution ===
if __name__ == "__main__":
    folder = r"C:\Users\<USER>\Desktop\word_summary\word_folder"  
    output_excel = "feedback_summary.xlsx"

    df_result = read_docx_files(folder)
    save_to_excel(df_result, output_excel)
